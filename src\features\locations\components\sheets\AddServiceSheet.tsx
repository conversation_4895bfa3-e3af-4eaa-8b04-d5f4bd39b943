import { useState } from "react";
import { X, User, Video, Volume2, Info } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { ChevronRight } from "lucide-react";
import type { CreateServiceRequest } from "../../types";
import { ExpirationSettings } from "@/components/common/ExpirationSettings";
import { LocationStationAccordion } from "@/components/common/LocationStationAccordion";
import type {
	Location,
	ApplyServiceOption,
} from "@/components/common/LocationStationAccordion";

interface AddServiceSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: CreateServiceRequest) => void;
}

export function AddServiceSheet({
	open,
	onOpenChange,
	onSubmit,
}: AddServiceSheetProps) {
	const [currentStep, setCurrentStep] = useState(2);
	const [formData, setFormData] = useState<CreateServiceRequest>({
		serviceName: "",
		description: "",
		autoApprove: false,
		serviceVisibility: true,
		serviceAvailability: true,
		availableMethods: ["in-person"],
		serviceDuration: 0,
		durationUnit: "minutes",
		applyServiceTo: "all-locations",
		selectedLocations: [],
	});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [selectedLocationIds, setSelectedLocationIds] = useState<Set<string>>(
		new Set()
	);
	const [selectedStationIds, setSelectedStationIds] = useState<Set<string>>(
		new Set()
	);

	const availableMethods = [
		{ id: "in-person", label: "In Person", icon: User },
		{ id: "video", label: "Video", icon: Video },
		{ id: "audio", label: "Audio", icon: Volume2 },
	];

	// Mock data for locations and stations
	const mockLocations: Location[] = [
		{
			id: "loc-1",
			name: "Downtown Medical Center",
			stations: [
				{ id: "station-1", name: "Emergency Room" },
				{ id: "station-2", name: "Cardiology Wing" },
				{ id: "station-3", name: "Radiology Department" },
				{ id: "station-4", name: "Laboratory" },
			],
		},
		{
			id: "loc-2",
			name: "North Campus Clinic",
			stations: [
				{ id: "station-5", name: "General Practice" },
				{ id: "station-6", name: "Pediatrics" },
			],
		},
		{
			id: "loc-3",
			name: "Westside Health Hub",
			stations: [
				{ id: "station-7", name: "Urgent Care" },
				{ id: "station-8", name: "Pharmacy" },
				{ id: "station-9", name: "Physical Therapy" },
			],
		},
		{
			id: "loc-4",
			name: "East Medical Plaza",
			stations: [
				{ id: "station-10", name: "Orthopedics" },
				{ id: "station-11", name: "Dermatology" },
			],
		},
	];

	const handleInputChange = (
		field: keyof CreateServiceRequest,
		value: any
	) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleMethodToggle = (methodId: string) => {
		setFormData((prev) => ({
			...prev,
			availableMethods: prev.availableMethods.includes(methodId)
				? prev.availableMethods.filter((id) => id !== methodId)
				: [...prev.availableMethods, methodId],
		}));
	};

	const handleLocationSelection = (locationId: string, checked: boolean) => {
		setSelectedLocationIds((prev) => {
			const newSet = new Set(prev);
			if (checked) {
				newSet.add(locationId);
			} else {
				newSet.delete(locationId);
			}
			return newSet;
		});
	};

	const handleStationSelection = (stationId: string, checked: boolean) => {
		setSelectedStationIds((prev) => {
			const newSet = new Set(prev);
			if (checked) {
				newSet.add(stationId);
			} else {
				newSet.delete(stationId);
			}
			return newSet;
		});
	};

	const handleSubmit = async () => {
		if (!formData.serviceName.trim()) {
			return;
		}

		setIsSubmitting(true);
		try {
			await onSubmit?.(formData);

			// Reset form
			setFormData({
				serviceName: "",
				description: "",
				autoApprove: false,
				serviceVisibility: true,
				serviceAvailability: true,
				availableMethods: ["in-person"],
				serviceDuration: 0,
				durationUnit: "minutes",
				applyServiceTo: "all-locations",
				selectedLocations: [],
			});
			setCurrentStep(1);
			onOpenChange(false);
		} catch (error) {
			console.error("Error submitting service:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleCancel = () => {
		// Reset form
		setFormData({
			serviceName: "",
			description: "",
			autoApprove: false,
			serviceVisibility: true,
			serviceAvailability: true,
			availableMethods: ["in-person"],
			serviceDuration: 0,
			durationUnit: "minutes",
			applyServiceTo: "all-locations",
			selectedLocations: [],
		});
		setCurrentStep(1);
		onOpenChange(false);
	};

	const handleNext = () => {
		if (currentStep < 3) {
			setCurrentStep(currentStep + 1);
		} else {
			handleSubmit();
		}
	};

	const canProceed = () => {
		if (currentStep === 1) {
			return formData.serviceName.trim().length > 0;
		}
		return true;
	};

	const renderStepContent = () => {
		switch (currentStep) {
			case 1:
				return (
					<div className="space-y-6">
						{/* Service Name */}
						<div className="space-y-1.5">
							<label className="text-sm font-semibold text-[#323539]">
								Service Name{" "}
								<span className="text-red-500">*</span>
							</label>
							<InputText
								placeholder="Blood test"
								value={formData.serviceName}
								onChange={(e) =>
									handleInputChange(
										"serviceName",
										e.target.value
									)
								}
								className="w-full border-[#e5e5e7] text-[#323539]"
								id="service-name"
								variant="default"
							/>
						</div>

						{/* Description */}
						<div className="space-y-1.5">
							<label className="text-sm font-semibold text-[#323539]">
								Description
							</label>
							<Textarea
								placeholder="Blood test"
								value={formData.description}
								onChange={(e) =>
									handleInputChange(
										"description",
										e.target.value
									)
								}
								className="min-h-[114px] w-full resize-none border-[#e5e5e7] text-[#323539]"
							/>
						</div>

						{/* Select Preferences */}
						<div className="space-y-2">
							<h3 className="text-sm font-medium text-[#323539]">
								Select Preferences
							</h3>
							<div className="space-y-0">
								{/* Auto Approve */}
								<div className="flex items-center justify-between border-b-[0.5px] border-[#bfbfbf] px-6 py-3">
									<div className="flex items-center gap-2">
										<span className="text-sm text-zinc-900">
											Auto Approve
										</span>
										<Info className="h-3 w-3 text-zinc-400" />
									</div>
									<div className="flex items-center gap-1.5">
										<Switch
											checked={formData.autoApprove}
											onCheckedChange={(checked) =>
												handleInputChange(
													"autoApprove",
													checked
												)
											}
										/>
										<span className="text-xs text-zinc-500">
											{formData.autoApprove
												? "On"
												: "Off"}
										</span>
									</div>
								</div>

								{/* Service Visibility */}
								<div className="flex items-center justify-between border-b border-[#bfbfbf] px-6 py-3">
									<div className="flex items-center gap-2">
										<span className="text-sm text-zinc-900">
											Service Visibility
										</span>
										<Info className="h-3 w-3 text-zinc-400" />
									</div>
									<div className="flex items-center gap-1.5">
										<Switch
											checked={formData.serviceVisibility}
											onCheckedChange={(checked) =>
												handleInputChange(
													"serviceVisibility",
													checked
												)
											}
										/>
										<span className="text-xs text-zinc-500">
											{formData.serviceVisibility
												? "On"
												: "Off"}
										</span>
									</div>
								</div>

								{/* Service Availability */}
								<div className="flex items-center justify-between px-6 py-3">
									<div className="flex items-center gap-2">
										<span className="text-sm text-zinc-900">
											Service Availability
										</span>
										<Info className="h-3 w-3 text-zinc-400" />
									</div>
									<div className="flex items-center gap-1.5">
										<Switch
											checked={
												formData.serviceAvailability
											}
											onCheckedChange={(checked) =>
												handleInputChange(
													"serviceAvailability",
													checked
												)
											}
										/>
										<span className="text-xs text-zinc-500">
											{formData.serviceAvailability
												? "On"
												: "Off"}
										</span>
									</div>
								</div>
							</div>
						</div>

						{/* Service Available in Methods */}
						<div className="space-y-5 border-b border-[#bfbfbf] pb-3">
							<div className="space-y-2">
								<h3 className="text-sm font-medium text-zinc-900">
									Service Available in Methods
								</h3>
								<div className="flex gap-3">
									{availableMethods.map((method) => {
										const Icon = method.icon;
										const isSelected =
											formData.availableMethods.includes(
												method.id
											);
										return (
											<button
												key={method.id}
												type="button"
												onClick={() =>
													handleMethodToggle(
														method.id
													)
												}
												className={`flex items-center gap-2.5 rounded-md border-[0.5px] p-2 ${
													isSelected
														? "border-zinc-100 bg-white"
														: "border-zinc-200 bg-white"
												}`}
											>
												<Icon className="h-3 w-3" />
												<span className="text-xs font-medium text-zinc-800">
													{method.label}
												</span>
												{/* <div className="h-3 w-3"> */}
												<Checkbox
													checked={isSelected}
													onCheckedChange={() =>
														handleMethodToggle(
															method.id
														)
													}
													className="h-3 w-3"
												/>
												{/* </div> */}
											</button>
										);
									})}
								</div>
							</div>
						</div>

						{/* Service Duration */}
						<div className="flex items-start justify-between">
							<label className="mt-1 text-sm font-medium text-[#323539]">
								Service Duration
							</label>
							{/* Expiration Section */}
							<ExpirationSettings
								value={formData.serviceDuration.toString()}
								onValueChange={(value) =>
									handleInputChange("serviceDuration", value)
								}
								unit={formData.durationUnit}
								onUnitChange={(unit) =>
									handleInputChange("durationUnit", unit)
								}
								useSpecificDate={false}
								onUseSpecificDateChange={() => {}}
								label=""
							/>
						</div>
					</div>
				);
			case 2:
				return (
					<LocationStationAccordion
						locations={mockLocations}
						selectedLocationIds={selectedLocationIds}
						selectedStationIds={selectedStationIds}
						onLocationSelectionChange={handleLocationSelection}
						onStationSelectionChange={handleStationSelection}
					/>
				);
			case 3:
				return (
					<div className="space-y-6">
						<div className="text-center">
							<h3 className="text-lg font-semibold">Step 3</h3>
							<p className="text-sm text-gray-500">
								Final service setup will go here
							</p>
						</div>
					</div>
				);
			default:
				return null;
		}
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full overflow-y-auto px-9 py-9 sm:max-w-[525px] [&>button]:hidden">
				<SheetHeader className="p-0">
					<div className="flex items-center justify-between">
						<SheetTitle className="text-2xl font-semibold tracking-tight text-zinc-950">
							Add New Service
						</SheetTitle>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="h-6 w-6 rounded-sm"
						>
							<X className="h-3.5 w-3.5" />
							<span className="sr-only">Close</span>
						</Button>
					</div>
					<p className="text-[15px] text-zinc-500">
						{currentStep === 1
							? "Add Service information Below"
							: "Select which Service Providers provide this service"}
					</p>
				</SheetHeader>

				<div className="flex-1 space-y-5 pb-6">
					{renderStepContent()}
				</div>

				{/* Footer */}
				<SheetFooter className="mt-6 flex-row justify-end gap-3 p-0">
					<Button
						variant="outline"
						onClick={handleCancel}
						className="h-10 w-20 cursor-pointer"
					>
						Cancel
					</Button>
					<Button
						className="h-10 w-20 cursor-pointer bg-[#005893] hover:bg-[#005893]/90"
						onClick={handleNext}
						disabled={isSubmitting || !canProceed()}
					>
						{isSubmitting
							? "Saving..."
							: currentStep === 2
								? "Add"
								: currentStep === 3
									? "Save"
									: "Next"}
					</Button>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
