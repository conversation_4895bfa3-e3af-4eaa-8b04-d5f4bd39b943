import { useState } from "react";
import { ChevronLeft } from "lucide-react";
import { InputText } from "@/components/common/InputText";

interface AddServiceProviderStepProps {
	onBack?: () => void;
	onSendInvite?: (data: ServiceProviderData) => void;
}

interface ServiceProviderData {
	firstName: string;
	lastName: string;
	email: string;
	phone: string;
}

export function AddServiceProviderStep({
	onBack,
	onSendInvite,
}: AddServiceProviderStepProps) {
	const [formData, setFormData] = useState<ServiceProviderData>({
		firstName: "",
		lastName: "",
		email: "",
		phone: "",
	});

	const handleInputChange = (
		field: keyof ServiceProviderData,
		value: string
	) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center gap-3">
				<button
					onClick={onBack}
					className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-md hover:bg-gray-100"
				>
					<ChevronLeft className="h-5 w-5 text-gray-600" />
				</button>
				<div>
					<h3 className="text-lg font-bold text-gray-900">
						Add New Service Provider User
					</h3>
					<p className="text-sm text-gray-500">
						Provide details to add a new Service Provider User
					</p>
				</div>
			</div>

			{/* Form Fields */}
			<div className="space-y-4">
				{/* First Name and Last Name */}
				<div className="grid grid-cols-2 gap-4">
					<div className="space-y-1.5">
						<label className="text-sm font-medium text-gray-900">
							First name <span className="text-red-500">*</span>
						</label>
						<InputText
							placeholder="Enter first name"
							value={formData.firstName}
							onChange={(e) =>
								handleInputChange("firstName", e.target.value)
							}
							className="w-full border-gray-200"
							variant="default"
						/>
					</div>
					<div className="space-y-1.5">
						<label className="text-sm font-medium text-gray-900">
							Last name <span className="text-red-500">*</span>
						</label>
						<InputText
							placeholder="Enter last name"
							value={formData.lastName}
							onChange={(e) =>
								handleInputChange("lastName", e.target.value)
							}
							className="w-full border-gray-200"
							variant="default"
						/>
					</div>
				</div>

				{/* Email */}
				<div className="space-y-1.5">
					<label className="text-sm font-medium text-gray-900">
						Email
					</label>
					<InputText
						placeholder="Enter email address"
						type="email"
						value={formData.email}
						onChange={(e) =>
							handleInputChange("email", e.target.value)
						}
						className="w-full border-gray-200"
						variant="default"
					/>
				</div>

				{/* Phone */}
				<div className="space-y-1.5">
					<label className="text-sm font-medium text-gray-900">
						Phone
					</label>
					<InputText
						placeholder="Enter phone number"
						type="tel"
						value={formData.phone}
						onChange={(e) =>
							handleInputChange("phone", e.target.value)
						}
						className="w-full border-gray-200"
						variant="default"
					/>
				</div>
			</div>
		</div>
	);
}
