import { type FC } from "react";
import { Trash2, <PERSON><PERSON><PERSON>, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import type { Location } from "@/features/locations/types";
import { Badge } from "@/components/ui/badge";

export interface ServiceCardProps {
	location: Location;
	onEdit?: (location: Location) => void;
	onView?: (location: Location) => void;
	isSelected?: boolean;
	onSelectionChange?: (selected: boolean) => void;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
	location,
	onEdit,
	onView,
	isSelected = false,
	onSelectionChange,
}) => {
	return (
		<div
			className="hover:bg-foreground-muted flex cursor-pointer flex-wrap items-center justify-start border-b border-zinc-200 bg-white last:border-b-0"
			onClick={() => onView?.(location)}
		>
			{/* Checkbox Section */}
			<div
				className="flex h-16 items-center px-4"
				onClick={(e) => e.stopPropagation()}
			>
				<Checkbox
					checked={isSelected}
					onCheckedChange={onSelectionChange}
					className="cursor-pointer"
				/>
			</div>

			{/* Service Name Section */}
			<div className="flex flex-2 items-center px-3">
				<h2 className="text-base leading-5 font-medium">
					{location.name}
				</h2>
			</div>

			{/* Status Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className="border-transparent bg-[#c3efce] text-[#0a2914]"
				>
					Active
				</Badge>
			</div>

			{/* Form Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className="bg-foreground-muted border-transparent text-[#0a2914]"
				>
					12
				</Badge>
			</div>

			{/* Auto Approve Section */}
			<div className="flex flex-1 items-center gap-1.5 px-3">
				<Badge
					variant="outline"
					className="h-[7px] w-[7px] rounded-full border-transparent bg-[#c3efce] p-0"
				></Badge>
				<h3 className="text-base leading-5 font-normal">On</h3>
			</div>

			{/* Time Section */}
			<div className="flex flex-1 items-center px-3">
				<h3 className="text-base leading-5 font-normal">40 min</h3>
			</div>

			{/* Actions Section */}
			<div className="flex flex-1 items-center justify-end px-3">
				<div className="flex items-center gap-2.5">
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onEdit?.(location);
						}}
					>
						<SquarePen className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							console.log("QR Code action for:", location.name);
						}}
					>
						<Trash2 className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onView?.(location);
						}}
					>
						<ChevronRight className="h-4 w-4" />
					</Button>
				</div>
			</div>
		</div>
	);
};
