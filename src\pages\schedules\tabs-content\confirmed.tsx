import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { CiSearch } from "react-icons/ci";
import { GoTag } from "react-icons/go";
import { IoIosInformationCircleOutline } from "react-icons/io";
import { LuLayoutDashboard } from "react-icons/lu";
import { IoNotificationsOutline } from "react-icons/io5";
import { FiUser } from "react-icons/fi";
import { LuCalendar, LuCalendarSync } from "react-icons/lu";
import { LuClock } from "react-icons/lu";
import { MdOutlineNotificationsActive } from "react-icons/md";
import { FaRegCommentDots, FaRegTrashCan } from "react-icons/fa6";

export default function Confirmed() {
    return (
        <div className="w-full py-3 px-2.5 border border-solid border-[#E4E4E7] bg-[#F6F6F6DB] rounded-lg">

            {/* search input */}

            <div className="relative">
                <CiSearch className="absolute left-4 top-2.5 text-xl text-[#27272A]" />
                <input
                    type="text"
                    placeholder="Search"
                    className="w-full bg-white py-2 px-4 pl-10 rounded-lg border border-solid border-[#E4E4E7]"
                />
            </div>

            {/* actions */}

            <div className="flex items-center justify-between mt-3">
                <div className="flex items-center gap-x-2">
                    <Checkbox />
                    <label htmlFor="select-all" className="text-[#898EAA] text-sm">
                        Select All
                    </label>
                </div>
                <div className="space-x-2">
                    <Button variant="outline" className="py-3">
                        <IoNotificationsOutline size={16} color="#898EAA" />
                    </Button>
                    <Button variant="outline" className="py-3">
                        <FaRegTrashCan size={16} color="#898EAA" />
                    </Button>
                </div>

            </div>

            {/* appointment list*/}

            <div className="flex flex-col gap-y-5 mt-4">
                {Array.from({ length: 10 }).map((_, index) => (
                    <div key={index} className="border border-[#E4E4E7] p-3 bg-white rounded-xl space-y-3 transition-all duration-300 hover:border-[#128576] hover:shadow-[0px_-1px_6px_#128576]">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-x-2">
                                <Checkbox />
                                <div>
                                    <h3 className="text-[#18181B] text-sm font-regular">John Heatherway</h3>
                                </div>
                                <GoTag color="#8CA3B2" className="text-sm" />
                            </div>
                            <IoIosInformationCircleOutline color="#8CA3B2" className="text-base" />
                        </div>
                        <p className="text-[#52525B] text-sm mt-2 flex items-center gap-x-1">
                            <LuLayoutDashboard color="#52525B" className="text-sm" />
                            <span className="text-[#56758A]">
                                Dr. Abraham Johnson
                            </span>
                        </p>
                        <h1 className="flex items-center gap-x-2">
                            <FiUser color="#042C4D" />
                            <span className="text-[#042C4D]">
                                First CT Scan Appointment
                            </span>
                        </h1>
                        <div className="flex items-center gap-x-4 text-[#27272A]">
                            <div className="flex items-center gap-x-1">
                                <LuCalendar className="opacity-50" />
                                <p className="opacity-80">12 Apr 2025</p>
                            </div>
                            <div className="flex items-center gap-x-1">
                                <LuClock className="opacity-50" />
                                <p className="opacity-80">
                                    12:30 pm - 4:00 pm
                                </p>
                            </div>
                        </div>
                        <div className="pt-1 pb-2 flex items-center justify-between">
                            <div className="flex items-center gap-x-3 text-[#27272A] opacity-50">
                                <FaRegCommentDots size={18} />
                                <LuCalendarSync size={18} />
                                <FaRegTrashCan size={18} />
                            </div>
                            <Button variant="outline" className="py-3 px-4">
                                <MdOutlineNotificationsActive color="#005893" />
                            </Button>
                        </div>
                    </div>
                ))}
            </div>

        </div>
    )
}