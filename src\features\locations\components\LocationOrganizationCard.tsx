import React from "react";
import {
	Map<PERSON>in,
	Users,
	Hospital,
	Info,
	Edit,
	Trash2,
	Timer,
	ImageUp,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { StarRating } from "@/components/common/StarRating";

export interface Organization {
	id: string;
	name: string;
	address: string;
	rating: number;
	locationsCount: number;
	providersCount: number;
	averageWaitTime: string;
	logo?: string;
}

export interface LocationOrganizationCardProps {
	organization: Organization;
	onView?: (organization: Organization) => void;
	onEdit?: (organization: Organization) => void;
	onDelete?: (organization: Organization) => void;
	className?: string;
}

export const LocationOrganizationCard: React.FC<
	LocationOrganizationCardProps
> = ({ organization, onView, onEdit, onDelete, className = "" }) => {
	const handleView = () => onView?.(organization);
	const handleEdit = () => onEdit?.(organization);
	const handleDelete = () => {
		if (
			window.confirm(
				`Are you sure you want to delete ${organization.name}?`
			)
		) {
			onDelete?.(organization);
		}
	};

	return (
		<div
			className={`rounded-xl bg-white p-3 transition-shadow ${className} shadow-[0px_4px_20px_0px_hsla(204,15%,66%,0.18)]`}
		>
			<div className="flex items-center">
				{/* Left side - Logo and info */}
				<div className="flex flex-1 items-start gap-3">
					{/* Organization Logo/Icon */}
					<div
						className={cn(
							"border-border-muted flex size-[100px] flex-shrink-0 rounded-lg border",
							organization.logo
								? "items-center justify-center bg-white"
								: "items-start justify-start bg-[#FAFAFA] p-1"
						)}
					>
						{organization.logo ? (
							<img
								src={organization.logo}
								alt={`${organization.name} logo`}
								className="h-8 w-8 object-contain"
							/>
						) : (
							<div className="flex items-center justify-center rounded-md bg-[#09244B0A] p-2">
								<ImageUp className="h-6 w-6 text-base" />
							</div>
						)}
					</div>

					{/* Organization details */}
					<div className="flex min-w-0 flex-1 flex-col gap-1">
						<div className="flex flex-col gap-2">
							{/* Name and rating */}
							<div className="mb-1 flex items-center gap-2">
								<h3 className="truncate font-semibold text-gray-900">
									{organization.name}
								</h3>
								<StarRating rating={organization.rating} />
							</div>

							{/* Address */}
							<div className="mb-3 flex items-center gap-1 text-[13px] text-[#6D748D]">
								<MapPin className="h-4 w-4 flex-shrink-0" />
								<span className="truncate">
									{organization.address}
								</span>
							</div>
						</div>

						{/* Stats */}
						<div className="flex items-center gap-5">
							<div className="flex items-center gap-2 rounded-md bg-[#EEEFF6]/50 px-2 text-[15px]">
								<Hospital className="h-4 w-4" />
								<span className="font-medium text-[#6D748D]">
									Locations
								</span>
								<span className="font-semibold">
									{organization.locationsCount
										.toString()
										.padStart(2, "0")}
								</span>
							</div>

							<div className="flex items-center gap-2 rounded-md bg-[#EEEFF6]/50 px-2 text-[15px]">
								<Users className="h-4 w-4" />
								<span className="font-medium text-[#6D748D]">
									Providers
								</span>
								<span className="font-semibold">
									{organization.providersCount
										.toString()
										.padStart(2, "0")}
								</span>
							</div>
						</div>
					</div>
				</div>

				{/* Right side - Actions and wait time */}
				<div className="flex flex-1 flex-col items-end justify-between space-y-7">
					{/* Action buttons */}
					<div className="flex items-center gap-3">
						<Button
							variant="outline"
							size="sm"
							className="bg-foreground-muted hover:bg-foreground-muted/50 h-10 w-10 cursor-pointer border-none p-0 text-base"
							onClick={handleView}
						>
							<Info className="h-4 w-4" />
						</Button>

						<Button
							variant="outline"
							size="sm"
							className="bg-foreground-muted hover:bg-foreground-muted/50 h-10 w-10 cursor-pointer border-none p-0 text-base"
							onClick={handleEdit}
						>
							<Edit className="h-4 w-4" />
						</Button>

						<Button
							variant="outline"
							size="sm"
							className="bg-foreground-muted hover:bg-foreground-muted/50 h-10 w-10 cursor-pointer border-none p-0 text-base"
							onClick={handleDelete}
						>
							<Trash2 className="h-4 w-4" />
						</Button>
					</div>

					{/* Average wait time */}
					<div className="flex gap-2 text-right">
						<div className="flex items-center gap-1 text-sm text-gray-500">
							<Timer
								className="h-4 w-4 text-base"
								color="black"
							/>
							<span>Avg. Wait Time</span>
						</div>
						<div className="text-sm font-semibold text-gray-900">
							{organization.averageWaitTime}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};
