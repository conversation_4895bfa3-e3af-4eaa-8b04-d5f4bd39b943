import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RefactorMultiSelect } from "@/pages/schedules/components/custom-select";
import { providers, services } from "@/pages/schedules/db";
import { useState } from "react";
import { FiUser } from "react-icons/fi";
import { GoCheckCircleFill } from "react-icons/go";
import { MdOutlineVideocam } from "react-icons/md";
import { PiSpeakerHighThin } from "react-icons/pi";

export default function FirstAvailable({
    back,
    next
}: { back: () => void; next: () => void }) {
    const isProviderSingle = false;
    const isProviderMultiple = true;
    const [defaultDate, setDefaultDate] = useState<Date>();

    return (
        <div className="mx-5 mt-5">

            <div className="space-y-6">
                <div className="flex flex-col gap-2">
                    <Label htmlFor="services" className="text-[#18181B] text-sm font-normal">Select Service</Label>

                    <RefactorMultiSelect
                        value={""}
                        setValue={() => { }}
                        placeholder=" "
                        label="Services"

                        options={services}
                    />
                </div>
                <div>
                    <div className="flex flex-col gap-2">
                        <Label htmlFor="providers" className="text-[#18181B] text-sm font-normal">Select Provider</Label>

                        <RefactorMultiSelect
                            value={""}
                            setValue={() => { }}
                            placeholder=" "
                            label="Providers"

                            options={providers}
                        />
                    </div>
                    <p className="text-xs text-[#71717A]">
                        Select a provider you want to book. You can select multiple providers together view all available time slots.
                    </p>
                </div>
                <div className="flex flex-col gap-3">
                    <Label htmlFor="appointmentType" className="text-[#18181B] text-sm font-normal">Appointment Method</Label>
                    <div className="grid grid-cols-3 gap-2">
                        {["in-person", "audio", "video"].map((type, index) => (
                            <button
                                type="button"
                                key={index}
                                className={`cursor-pointer border flex items-center justify-between py-3 px-4 rounded-xl font-regular opacity-100 gap-x-3 text-center ${type === "in-person" ? "border-[#005893] bg-[#0058931A] text-[#005893]" : "border-[#D4D4D8]"}`}
                            // onClick={() => form.setValue("appointmentType", type as "in-person" | "audio" | "video")}
                            >
                                {type === "in-person" && <FiUser color="#005893" className="text-xl" />}
                                {type === "audio" && <PiSpeakerHighThin color="#005893" className="text-xl" />}
                                {type === "video" && <MdOutlineVideocam color="#005893" className="text-xl" />}
                                <span>{type.charAt(0).toUpperCase() + type.slice(1)}</span>
                                <GoCheckCircleFill color="#005893" />
                            </button>
                        ))}
                    </div>
                </div>
            </div>
            <div className="mt-16 mb-5">
                {/* <h1 className="text-base font-medium">Select Appointment Date & Time</h1> */}
                <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-4">
                        <Label htmlFor="appointmentDate" className="text-[#323539] text-sm font-normal border-b border-[#E4E4E7] pb-5">Select Appointment Date</Label>

                        <style>
                            {`
                                .picker-bk-appointment > div[data-slot] {
	                                width: 100% !important;
                                }
                            `}
                        </style>
                        <DatePicker
                            variant="input-only"
                            value={defaultDate}
                            onChange={date => setDefaultDate(date as Date)}
                        />

                        <DatePicker
                            variant="inline"
                            value={defaultDate}
                            onChange={date => setDefaultDate(date as Date)}
                            className="w-full border rounded-lg inline-block overflow-hidden scrollbar-hide picker-bk-appointment"
                        />

                    </div>

                    <div className="space-y-4">
                        <Label htmlFor="appointmentDate" className="text-[#323539] text-sm font-normal border-b border-[#E4E4E7] pb-5">Recommended Providers</Label>

                        {isProviderSingle ? (
                            <div className="space-y-3">
                                <div className="border border-[#E4E4E7] py-2.5 px-3 rounded-[10px] flex items-start gap-x-3">
                                    <div className="size-12 bg-[#E4E4E7] text-[#A1A1AA] rounded-full grid place-content-center">
                                        AB
                                    </div>

                                    <div>
                                        <h1 className="font-medium text-base">Dr. Abraham Lincoln</h1>
                                        <div className="flex items-center text-sm gap-x-1.5 text-[#A1A1AA]">
                                            <p className="border-r border-[#A1A1AA] pr-1.5 h-[0.8rem] grid place-content-center">Service 1</p>
                                            <p className="border-r border-[#A1A1AA] pr-1.5 h-[0.8rem] grid place-content-center">Service 2</p>
                                            <p>Service 3</p>
                                        </div>
                                    </div>
                                </div>
                                <div className="grid grid-cols-2 gap-x-3 gap-y-4">
                                    {Array.from({ length: 6 }).map((_, index) => (
                                        <button
                                            key={index}
                                            tabIndex={0}
                                            className="cursor-pointer py-4 px-2 bg-[#FAFAFA] border border-[#E4E4E7] rounded-xl text-xs font-medium"
                                        >
                                            <span>09:15 AM - 9:30 AM</span>
                                        </button>
                                    ))}
                                </div>
                            </div>
                        ) : (
                            <div className="space-y-2 max-h-[24rem] overflow-scroll scrollbar-hide">
                                {Array.from({ length: 6 }).map((_, index) => (
                                    <div
                                        key={index}
                                        className="w-full border border-[#E4E4E7] py-2 px-3 rounded-[10px] flex items-start gap-x-1"
                                    >
                                        <div className="shrink-0 size-12 bg-[#E4E4E7] text-[#A1A1AA] rounded-full grid place-content-center">
                                            AB
                                        </div>

                                        <div className="space-y-1.5">
                                            <h1 className="font-medium text-base">Dr. Abraham Lincoln</h1>
                                            <div className="max-w-[17rem] flex gap-x-2 overflow-scroll scrollbar-hide">
                                                {Array.from({ length: 6 }).map((_, index) => (
                                                    <button
                                                        key={index}
                                                        tabIndex={0}
                                                        className="whitespace-nowrap cursor-pointer py-2 px-2 bg-[#FAFAFA] border border-[#E4E4E7] rounded-xl text-xs font-medium"
                                                    >
                                                        <span>09:15 AM - 9:30 AM</span>
                                                    </button>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                </div>
            </div>

            <div className="flex items-center justify-end gap-x-3">
                <Button
                    type="button"
                    variant="outline"
                    onClick={back}
                    className="bg-[#F4F4F5] cursor-pointer"
                >
                    Back
                </Button>
                <Button
                    type="button"
                    className="cursor-pointer"
                    onClick={next}
                >
                    Next
                </Button>
            </div>
        </div>
    )
}