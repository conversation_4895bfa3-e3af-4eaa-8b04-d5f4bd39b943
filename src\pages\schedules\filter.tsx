import { useState } from "react";
import {
    Ta<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Ta<PERSON>Trigger,
} from "@/components/ui/tabs"
import clsx from "clsx";
import Confirmed from "./tabs-content/confirmed";
import Pending from "./tabs-content/pending";
import FollowUp from "./tabs-content/follow-up";

export default function Filter() {
    const [activeTab, setActiveTab] = useState("confirmed");

    return (
        <div className="w-full">
            <Tabs
                defaultValue="confirmed"
                value={activeTab}
                onValueChange={setActiveTab}
            >
                <TabsList className="gap-x-2 px-1.5 mb-4">
                    <TabsTrigger
                        value="confirmed"
                        className={clsx("cursor-pointer border border-[#F4F4F5]", activeTab === "confirmed" && "!bg-[#128576] text-white border-[#128576]")}
                        onClick={() => setActiveTab("confirmed")}
                    >Confirmed</TabsTrigger>
                    <TabsTrigger
                        value="pending"
                        className={clsx("cursor-pointer border border-[#F4F4F5]", activeTab === "pending" && "!bg-[#DC2626] text-white border-[#DC2626]")}
                        onClick={() => setActiveTab("pending")}
                    >Pending
                        <span className={clsx("bg-[#DC2626] text-white rounded-full px-1.5 py-1 text-xs", activeTab === "pending" && "!bg-[#FFF] !text-[#DC2626]")}>10</span>
                    </TabsTrigger>
                    <TabsTrigger
                        value="followup"
                        className={clsx("border border-[#F4F4F5]", activeTab === "followup" && "!bg-[#D09303] text-white border-[#FFC943]")}
                        onClick={() => setActiveTab("followup")}
                    >Follow Up
                        <span className={clsx("bg-[#D09303] text-white rounded-full px-1.5 py-1 text-xs", activeTab === "followup" && "!bg-[#FFF] !text-[#D09303]")}>10</span>
                    </TabsTrigger>
                </TabsList>
                <div className="max-h-screen overflow-y-scroll scrollbar-hide">
                    <TabsContent value="confirmed">
                        <Confirmed />
                    </TabsContent>
                    <TabsContent value="pending">
                        <Pending />
                    </TabsContent>
                    <TabsContent value="followup">
                        <FollowUp />
                    </TabsContent>
                </div>
            </Tabs>
        </div>
    );
}