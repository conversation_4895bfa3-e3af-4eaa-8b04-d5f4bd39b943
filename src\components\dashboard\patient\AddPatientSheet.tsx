import React, { useState, useEffect } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X, Check } from "lucide-react";
import { Label } from "@/components/ui/label";
import { UploadCard } from "@/components/ui-components/Upload";
import { useBusinessAttributesForForm } from "@/hooks/useBusinessAttributes";
import { useCreateClient } from "@/hooks/useClients";
import type { BusinessAttribute } from "@/types/businessAttributes";

interface AddPatientSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: PatientFormData) => void;
}

export interface PatientFormData {
	firstName: string;
	lastName: string;
	email: string;
	phone: string;
	image?: File;
	customFields?: Record<string, string | number | boolean>;
}

export function AddPatientSheet({
	open,
	onOpenChange,
	onSubmit,
}: AddPatientSheetProps) {
	const { data: businessAttributesData, isLoading: isLoadingAttributes } =
		useBusinessAttributesForForm();
	const businessAttributes: BusinessAttribute[] =
		(businessAttributesData as any)?.data || [];

	const [formData, setFormData] = useState<PatientFormData>({
		firstName: "",
		lastName: "",
		email: "",
		phone: "",
		customFields: {},
	});
	const [file, setFile] = useState<File | null>(null);
	const [isSuccess, setIsSuccess] = useState(false);

	const createClientMutation = useCreateClient({
		onSuccess: () => {
			setIsSuccess(true);
			onSubmit?.({
				...formData,
				image: file || undefined,
			});
		},
		onError: (error) => {
			console.error("Error creating client:", error);
		},
	});

	useEffect(() => {
		if (businessAttributes.length > 0) {
			const initialCustomFields: Record<
				string,
				string | number | boolean
			> = {};
			businessAttributes.forEach((attr) => {
				switch (attr.type) {
					case "checkbox":
						initialCustomFields[attr.key] = false;
						break;
					case "number":
						initialCustomFields[attr.key] = "";
						break;
					default:
						initialCustomFields[attr.key] = "";
				}
			});
			setFormData((prev) => ({
				...prev,
				customFields: initialCustomFields,
			}));
		}
	}, [businessAttributes]);

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value, type, checked } = e.target;

		if (name.startsWith("customFields.")) {
			const fieldName = name.split(".")[1];
			let fieldValue: string | number | boolean = value;
			if (type === "checkbox") {
				fieldValue = checked;
			} else if (type === "number") {
				fieldValue = value === "" ? "" : Number(value);
			}

			setFormData({
				...formData,
				customFields: {
					...formData.customFields,
					[fieldName]: fieldValue,
				},
			});
		} else {
			setFormData({
				...formData,
				[name]: value,
			});
		}
	};

	const handleFileUpload = () => {
		const input = document.createElement("input");
		input.type = "file";
		input.accept = ".svg,.png,.jpg,.jpeg";
		input.onchange = (e) => {
			const target = e.target as HTMLInputElement;
			if (target.files && target.files[0]) {
				setFile(target.files[0]);
			}
		};
		input.click();
	};

	const handleDragOver = (e: React.DragEvent) => {
		e.preventDefault();
	};

	const handleDrop = (e: React.DragEvent) => {
		e.preventDefault();
		if (e.dataTransfer.files && e.dataTransfer.files[0]) {
			setFile(e.dataTransfer.files[0]);
		}
	};

	const handleRemoveFile = () => {
		setFile(null);
	};

	const handleChangeFile = () => {
		handleFileUpload();
	};

	const resetForm = () => {
		const resetCustomFields: Record<string, string | number | boolean> = {};
		businessAttributes.forEach((attr) => {
			switch (attr.type) {
				case "checkbox":
					resetCustomFields[attr.key] = false;
					break;
				case "number":
					resetCustomFields[attr.key] = "";
					break;
				default:
					resetCustomFields[attr.key] = "";
			}
		});

		setFormData({
			firstName: "",
			lastName: "",
			email: "",
			phone: "",
			customFields: resetCustomFields,
		});
		setFile(null);
		setIsSuccess(false);
	};

	const handleSubmit = async () => {
		if (
			!formData.firstName ||
			!formData.lastName ||
			!formData.email ||
			!formData.phone
		) {
			return;
		}
		const clientData = {
			first_name: formData.firstName,
			last_name: formData.lastName,
			email: formData.email,
			phone_number: formData.phone,
			attributes: formData.customFields || {},
		};

		createClientMutation.mutate(clientData);
	};

	const handleAddAnother = () => {
		resetForm();
	};

	const handleDone = () => {
		resetForm();
		onOpenChange(false);
	};

	const handleClose = () => {
		resetForm();
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={handleClose}>
			<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
				<div className="flex h-full flex-col gap-6">
					<SheetHeader className="flex-row items-center justify-between space-y-0 px-0">
						<div className="flex flex-col gap-2">
							<SheetTitle className="text-base font-semibold">
								Add New Patient
							</SheetTitle>
							<p className="text-xs text-gray-500">
								Fill out the information below to add a patient.
							</p>
						</div>
						<Button
							variant="ghost"
							size="icon"
							onClick={handleClose}
							className="h-9 w-9 rounded-md"
						>
							<X className="h-4 w-4" />
						</Button>
					</SheetHeader>

					{isSuccess ? (
						<div className="flex flex-1 flex-col items-center justify-center gap-8">
							<div className="flex flex-col items-center gap-11">
								<div className="rounded-full bg-[#005893]/20 p-8">
									<Check className="h-12 w-12 text-[#005893]" />
								</div>
								<div className="flex w-full max-w-72 flex-col items-center gap-3">
									<h2 className="text-center text-xl font-semibold">
										Patients Added
									</h2>
									<p className="text-center text-sm">
										A new patients has been added
										successfully.
									</p>
								</div>
							</div>
							<div className="flex justify-center gap-3">
								<Button
									variant="secondary"
									onClick={handleAddAnother}
									className="h-9"
								>
									Add Another Patients
								</Button>
								<Button
									onClick={handleDone}
									className="h-9 w-20 bg-[#005893] hover:bg-[#004a7a]"
								>
									Done
								</Button>
							</div>
						</div>
					) : (
						<>
							<div className="flex-1">
								<div className="space-y-4">
									<div className="space-y-2">
										<Label
											htmlFor="firstName"
											className="text-xs"
										>
											First Name *
										</Label>
										<Input
											id="firstName"
											name="firstName"
											value={formData.firstName}
											onChange={handleInputChange}
											className="h-9 text-xs"
											placeholder="Enter first name"
										/>
									</div>

									<div className="space-y-2">
										<Label
											htmlFor="lastName"
											className="text-xs"
										>
											Last Name *
										</Label>
										<Input
											id="lastName"
											name="lastName"
											value={formData.lastName}
											onChange={handleInputChange}
											className="h-9 text-xs"
											placeholder="Enter last name"
										/>
									</div>
									<div className="space-y-2">
										<Label
											htmlFor="email"
											className="text-xs"
										>
											Email Address *
										</Label>
										<Input
											id="email"
											name="email"
											type="email"
											value={formData.email}
											onChange={handleInputChange}
											className="h-9 text-xs"
											placeholder="Enter email address"
										/>
									</div>
									<div className="space-y-2">
										<Label
											htmlFor="phone"
											className="text-xs"
										>
											Phone Number *
										</Label>
										<div className="flex h-9 items-center rounded-md border border-gray-200 px-3">
											<div className="flex items-center gap-1.5">
												<div className="h-6 w-6 overflow-hidden rounded-full">
													<img
														src="https://placehold.co/24x24"
														alt="Flag"
														className="h-full w-full object-cover"
													/>
												</div>
												<span className="text-xs">
													+1
												</span>
											</div>
											<Input
												id="phone"
												name="phone"
												value={formData.phone}
												onChange={handleInputChange}
												className="h-full border-0 text-xs focus-visible:ring-0 focus-visible:ring-offset-0"
												placeholder="Enter phone number"
											/>
										</div>
									</div>
									<UploadCard
										variant="horizontal-compact"
										width="w-full"
										title="Click or drag file here to upload file"
										description="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
										buttonText="Browse"
										accept=".svg,.png,.jpg,.jpeg"
										onBrowseClick={handleFileUpload}
										onDragOver={handleDragOver}
										onDrop={handleDrop}
										isUploaded={!!file}
										fileName={file?.name}
										fileSize={
											file
												? `${(file.size / 1024 / 1024).toFixed(2)} MB`
												: undefined
										}
										onRemove={handleRemoveFile}
										onChange={handleChangeFile}
									/>
									{/* Business attributes fields */}
									{isLoadingAttributes ? (
										<div className="space-y-2">
											<div className="h-4 animate-pulse rounded bg-gray-200"></div>
											<div className="h-9 animate-pulse rounded bg-gray-200"></div>
										</div>
									) : (
										businessAttributes.map((attr) => (
											<div
												key={attr.id}
												className="space-y-2"
											>
												<Label
													htmlFor={attr.key}
													className="text-xs"
												>
													{attr.label}
													{attr.is_required && " *"}
												</Label>
												{attr.type === "checkbox" ? (
													<div className="flex items-center space-x-2">
														<input
															type="checkbox"
															id={attr.key}
															name={`customFields.${attr.key}`}
															checked={Boolean(
																formData
																	.customFields?.[
																	attr.key
																]
															)}
															onChange={
																handleInputChange
															}
															className="h-4 w-4"
														/>
														<Label
															htmlFor={attr.key}
															className="text-xs font-normal"
														>
															{attr.label}
														</Label>
													</div>
												) : (
													<Input
														id={attr.key}
														name={`customFields.${attr.key}`}
														type={
															attr.type ===
															"number"
																? "number"
																: "text"
														}
														value={String(
															formData
																.customFields?.[
																attr.key
															] || ""
														)}
														onChange={
															handleInputChange
														}
														className="h-9 text-xs"
														placeholder={`Enter ${attr.label.toLowerCase()}`}
														required={
															attr.is_required
														}
													/>
												)}
											</div>
										))
									)}
								</div>
							</div>

							<div className="flex justify-end gap-3 pb-5">
								<Button
									variant="outline"
									onClick={handleClose}
									className="h-9"
								>
									Close
								</Button>
								<Button
									onClick={handleSubmit}
									disabled={createClientMutation.isPending}
									className="h-9 bg-[#005893]"
								>
									{createClientMutation.isPending
										? "Saving..."
										: "Save Patients"}
								</Button>
							</div>
						</>
					)}
				</div>
			</SheetContent>
		</Sheet>
	);
}
