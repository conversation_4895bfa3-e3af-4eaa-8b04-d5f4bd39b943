import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
	categoriesApi,
	type CreateCategoryRequest,
	type UpdateCategoryRequest,
	type CategoriesFilters,
	type CategoryData,
	type CategoriesResponse,
	type CategoryDetailResponse,
} from "@/lib/api/categoriesApi";
import {
	queryKeys,
	defaultMutationOptions,
	mediumLivedQueryOptions,
} from "@/lib/query";
import { useUIStore } from "@/stores/uiStore";

export const useCategories = (
	filters: CategoriesFilters = {},
	options?: {
		enabled?: boolean;
	}
) => {
	return useQuery<CategoriesResponse>(
		queryKeys.categories.list(filters),
		() => categoriesApi.getCategories(filters),
		{
			...mediumLivedQueryOptions,
			enabled: options?.enabled !== false,
		}
	);
};



