// AUTO-GENERATED FROM SWAGGER (docs.json)

export interface User {
  id: number;
  name: string;
  email: string;
  intercom_user_hash?: string;
  is_email_verified: boolean;
  two_factor_enable?: boolean;
  business_count?: number;
}

export interface SuccessResponse<T = any> {
  success: true;
  message: string;
  data: T;
}

export interface ErrorResponse {
  success: false;
  message: string;
  errors?: string[];
}

export interface ValidationError {
  success: false;
  message: string;
  errors: Record<string, string[]>;
}

export interface TwoFactorResponse {
  token: string;
  expires_in: number;
  two_factor_skip: boolean;
  twoFactor?: boolean;
  two_factor_enable?: boolean;
}

// Register
export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
}
export type RegisterResponse = SuccessResponse<null>;

// Login
export interface LoginRequest {
  email: string;
  password: string;
}
export type LoginResponse = SuccessResponse<{
  token: string;
  expires_in: number;
  two_factor_skip: boolean;
  two_factor_enable: boolean;
  remember_token?: string;
  id: number;
  name: string;
  email: string;
  intercom_user_hash?: string;
  is_email_verified: boolean;
  business_count: number;
}>;

// Login Temp
export interface LoginTempRequest {
  token: string;
}
export type LoginTempResponse = SuccessResponse<any>; // Can be user or 2FA challenge

// 2FA Enable
export interface TwoFAEnableResponse extends SuccessResponse<{
  qrcode: string;
  code: string;
}> {}

// 2FA Confirm
export interface TwoFAConfirmRequest {
  code: string;
}
export interface TwoFAConfirmResponse {
  message: string;
  user: User;
}

// 2FA Verify
export interface TwoFAVerifyRequest {
  code: string;
  token: string;
}
export interface TwoFAVerifyResponse {
  message: string;
  user: User;
}

// 2FA Email OTP
export interface TwoFAEmailOTPRequest {
  token: string;
}
export interface TwoFAEmailOTPResponse {
  message: string;
}

// 2FA Disable
export interface TwoFADisableResponse {
  message: string;
}

// 2FA Status
export interface TwoFAStatusResponse {
  message: string;
}

// 2FA Skip
export interface TwoFASkipResponse {
  message: string;
}

// 2FA Reconfigure
export interface TwoFAReconfigureResponse {
  qrcode: string;
  code: string;
}

// Forgot Password
export interface ForgotPasswordRequest {
  email: string;
}
export interface ForgotPasswordResponse {
  message: string;
}

// Reset Password
export interface ResetPasswordRequest {
  password: string;
  password_confirmation: string;
  token: string;
}
export interface ResetPasswordResponse {
  message: string;
}

// Logout
export type LogoutResponse = SuccessResponse<null>; 