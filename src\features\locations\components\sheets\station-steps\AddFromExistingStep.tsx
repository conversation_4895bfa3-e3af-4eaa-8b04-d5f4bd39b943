import { useState } from "react";
import { ChevronLeft, Search } from "lucide-react";
import { InputText } from "@/components/common/InputText";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";

interface AddFromExistingStepProps {
	onBack?: () => void;
	onImport?: (selectedProviders: Provider[]) => void;
}

interface Provider {
	id: string;
	name: string;
	email: string;
	avatar?: string;
}

// Mock data - this would come from an API
const mockProviders: Provider[] = [
	{ id: "1", name: "Provider Name", email: "Provider Email" },
	{ id: "2", name: "Provider Name", email: "Provider Email" },
	{ id: "3", name: "Provider Name", email: "Provider Email" },
	{ id: "4", name: "Provider Name", email: "Provider Email" },
	{ id: "5", name: "Provider Name", email: "Provider Email" },
	{ id: "6", name: "Provider Name", email: "Provider Email" },
];

export function AddFromExistingStep({
	onBack,
	onImport,
}: AddFromExistingStepProps) {
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedProviders, setSelectedProviders] = useState<string[]>([]);

	const filteredProviders = mockProviders.filter(
		(provider) =>
			provider.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
			provider.email.toLowerCase().includes(searchQuery.toLowerCase())
	);

	const handleProviderSelection = (providerId: string, checked: boolean) => {
		if (checked) {
			setSelectedProviders((prev) => [...prev, providerId]);
		} else {
			setSelectedProviders((prev) =>
				prev.filter((id) => id !== providerId)
			);
		}
	};

	const handleImport = () => {
		const selectedProviderData = mockProviders.filter((provider) =>
			selectedProviders.includes(provider.id)
		);
		onImport?.(selectedProviderData);
	};

	const getInitials = (name: string) => {
		return name
			.split(" ")
			.map((word) => word.charAt(0))
			.join("")
			.toUpperCase()
			.substring(0, 2);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center gap-3">
				<button
					onClick={onBack}
					className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-md hover:bg-gray-100"
				>
					<ChevronLeft className="h-5 w-5 text-gray-600" />
				</button>
				<div>
					<h3 className="text-lg font-bold text-gray-900">
						Add from Existing
					</h3>
					<p className="text-sm text-gray-500">
						If the service provider stations already exists on the
						<br />
						platform, duplicate selected settings & calendar
					</p>
				</div>
			</div>

			{/* Search Bar */}
			<div className="pt-4">
				<div className="relative">
					<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
					<InputText
						placeholder="Select All Locations and Stations"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						className="w-full border-gray-200 pl-10"
						variant="default"
					/>
				</div>
			</div>

			{/* Provider List */}
			<div className="scrollbar-hide max-h-96 space-y-3 overflow-y-auto">
				{filteredProviders.map((provider) => (
					<label
						htmlFor={provider.id}
						key={provider.id}
						className="flex cursor-pointer items-center gap-3 rounded-lg border border-gray-200 p-3 hover:bg-gray-50"
					>
						<Avatar className="h-10 w-10 rounded-full">
							<AvatarImage src={provider.avatar} />
							<AvatarFallback className="bg-gray-100 text-sm font-medium text-gray-600">
								{getInitials(provider.name)}
							</AvatarFallback>
						</Avatar>
						<div className="flex-1">
							<div className="font-medium text-gray-900">
								{provider.name}
							</div>
							<div className="text-sm text-gray-500">
								{provider.email}
							</div>
						</div>
						<Checkbox
							checked={selectedProviders.includes(provider.id)}
							onCheckedChange={(checked) =>
								handleProviderSelection(
									provider.id,
									checked as boolean
								)
							}
							id={provider.id}
						/>
					</label>
				))}
			</div>

			{filteredProviders.length === 0 && (
				<div className="py-8 text-center">
					<p className="text-gray-500">
						No providers found matching your search.
					</p>
				</div>
			)}
		</div>
	);
}
