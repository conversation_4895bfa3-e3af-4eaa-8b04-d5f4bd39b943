import React, { useState } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { X, PaintBucket, CircleMinus, CirclePlus, Check } from "lucide-react";
import { Label } from "@/components/ui/label";
import {
	LocationSelectionStep,
	type LocationSelectionData,
} from "./LocationSelectionStep";

interface AddCategorySheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: CategoryFormData & LocationSelectionData) => void;
}

export interface CategoryFormData {
	name: string;
	description: string;
	color: string;
	conditions: CategoryCondition[];
}

export interface CategoryCondition {
	id: string;
	categoryCondition: string;
	conditionCheck: string;
	field: string;
	parameter: string;
	date: string;
}

const colorPalette = [
	"#222A31",
	"#5E98C9",
	"#0277D8",
	"#9A76C9",
	"#32BA3F",
	"#E36F6F",
	"#E9ED18",
	"#FF9500",
	"#C77676",
	"#FA38C4",
	"#54758F",
	"#A3762D",
];

export function AddCategorySheet({
	open,
	onOpenChange,
	onSubmit,
}: AddCategorySheetProps) {
	const [currentStep, setCurrentStep] = useState<
		"category" | "locations" | "success"
	>("category");
	const [formData, setFormData] = useState<CategoryFormData>({
		name: "Dermatology",
		description: "",
		color: "#000000",
		conditions: [
			{
				id: "1",
				categoryCondition: "Custom",
				conditionCheck: "Information Check",
				field: "Date of Birth",
				parameter: "Before the Date",
				date: "01 Jan 1995",
			},
			{
				id: "2",
				categoryCondition: "Custom",
				conditionCheck: "Registration",
				field: "Date of Birth",
				parameter: "Before the Date",
				date: "01 Jan 2020",
			},
		],
	});
	const [locationData, setLocationData] = useState<LocationSelectionData>({
		applyToAll: false,
		selectedLocations: [],
		selectedStations: {},
	});
	const [isSubmitting, setIsSubmitting] = useState(false);

	const resetForm = () => {
		setFormData({
			name: "",
			description: "",
			color: "#000000",
			conditions: [
				{
					id: "1",
					categoryCondition: "Custom",
					conditionCheck: "Information Check",
					field: "Date of Birth",
					parameter: "Before the Date",
					date: "01 Jan 1995",
				},
			],
		});
		setLocationData({
			applyToAll: true,
			selectedLocations: [],
			selectedStations: {},
		});
		setCurrentStep("category");
	};

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
	) => {
		const { name, value } = e.target;
		setFormData({
			...formData,
			[name]: value,
		});
	};

	const handleColorSelect = (color: string) => {
		setFormData({
			...formData,
			color,
		});
	};

	const handleConditionChange = (
		conditionId: string,
		field: string,
		value: string | string[]
	) => {
		const stringValue = Array.isArray(value) ? value[0] : value;
		setFormData({
			...formData,
			conditions: formData.conditions.map((condition) =>
				condition.id === conditionId
					? { ...condition, [field]: stringValue }
					: condition
			),
		});
	};

	const handleAddCondition = () => {
		if (formData.conditions.length < 5) {
			const newCondition: CategoryCondition = {
				id: Date.now().toString(),
				categoryCondition: "Custom",
				conditionCheck: "Information Check",
				field: "Date of Birth",
				parameter: "Before the Date",
				date: "01 Jan 1995",
			};
			setFormData({
				...formData,
				conditions: [...formData.conditions, newCondition],
			});
		}
	};

	const handleRemoveCondition = (conditionId: string) => {
		if (formData.conditions.length > 1) {
			setFormData({
				...formData,
				conditions: formData.conditions.filter(
					(condition) => condition.id !== conditionId
				),
			});
		}
	};

	const handleCategorySubmit = () => {
		if (!formData.name.trim()) {
			return;
		}
		setCurrentStep("locations");
	};

	const handleLocationSubmit = async () => {
		setIsSubmitting(true);
		try {
			setCurrentStep("success");
		} catch (error) {
			console.error("Error submitting category:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleSkipLocationSelection = async () => {
		setIsSubmitting(true);
		try {
			setCurrentStep("success");
		} catch (error) {
			console.error("Error submitting category:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		resetForm();
		onOpenChange(false);
	};

	const handleSheetOpenChange = (open: boolean) => {
		if (!open) {
			resetForm();
		}
		onOpenChange(open);
	};

	return (
		<Sheet open={open} onOpenChange={handleSheetOpenChange}>
			<SheetContent className="w-full !max-w-[525px] p-0 [&>button]:hidden">
				{currentStep === "success" ? (
					<div className="flex h-full max-h-screen flex-col">
						<div className="flex flex-shrink-0 flex-col gap-2.5 p-6 pb-0">
							<div className="flex h-14 items-start justify-start gap-2.5">
								<div className="flex flex-1 flex-col items-start justify-start gap-2">
									<div className="text-base leading-7 font-semibold">
										Add New Category
									</div>
									<div className="text-xs leading-none font-normal text-gray-500">
										Category information
									</div>
								</div>
								<div className="flex items-start justify-start gap-2.5">
									<Button
										variant="ghost"
										size="icon"
										onClick={handleClose}
										className="h-9 w-9 rounded-md"
									>
										<X className="h-4 w-4" />
									</Button>
								</div>
							</div>
						</div>
						<div className="flex flex-1 items-center justify-center">
							{renderSuccessContent()}
						</div>
					</div>
				) : (
					<div className="flex h-full max-h-screen flex-col">
						<div className="flex flex-shrink-0 flex-col gap-2.5 p-6 pb-0">
							<div className="flex h-14 items-start justify-start gap-2.5">
								<div className="flex flex-1 flex-col items-start justify-start gap-2">
									<div className="text-base leading-7 font-semibold">
										Add New Category
									</div>
									<div className="text-xs leading-none font-normal text-gray-500">
										Category information
									</div>
								</div>
								<div className="flex items-start justify-start gap-2.5">
									<Button
										variant="ghost"
										size="icon"
										onClick={handleClose}
										className="h-9 w-9 rounded-md"
									>
										<X className="h-4 w-4" />
									</Button>
								</div>
							</div>
						</div>
						<div className="min-h-0 flex-1 overflow-y-auto p-6 pt-6">
							{currentStep === "category" &&
								renderCategoryContent()}
							{currentStep === "locations" &&
								renderLocationContent()}
						</div>

						<div className="flex-shrink-0 bg-white p-6">
							{currentStep === "category"
								? renderCategoryFooter()
								: renderLocationFooter()}
						</div>
					</div>
				)}
			</SheetContent>
		</Sheet>
	);

	function renderCategoryContent() {
		return (
			<div className="flex flex-col gap-6">
				<div className="flex flex-col gap-4">
					<h3 className="text-sm font-semibold">Category Details</h3>

					<div className="space-y-2">
						<Label htmlFor="name" className="text-xs font-medium">
							Category Name *
						</Label>
						<Input
							id="name"
							name="name"
							value={formData.name}
							onChange={handleInputChange}
							className="h-9 text-xs"
							placeholder="Enter category name"
						/>
					</div>

					<div className="space-y-2">
						<Label
							htmlFor="description"
							className="text-xs font-medium"
						>
							Description
						</Label>
						<Textarea
							id="description"
							name="description"
							value={formData.description}
							onChange={handleInputChange}
							className="min-h-20 resize-none text-xs"
							placeholder="Category Description"
						/>
					</div>
				</div>
				<div className="flex flex-col gap-4">
					<h3 className="text-sm font-semibold">Assign Color</h3>

					<div className="flex items-center gap-2">
						<div className="flex h-9 items-center gap-4 rounded-md bg-gray-100 p-2">
							<PaintBucket className="h-5 w-5" />
							<div
								className="h-6 w-6 rounded-md"
								style={{
									backgroundColor: formData.color,
								}}
							/>
						</div>
						<Input
							value={formData.color}
							onChange={(e) => handleColorSelect(e.target.value)}
							className="h-9 w-24 text-xs"
							placeholder="#000000"
						/>
					</div>
					<div className="flex flex-col gap-1.5">
						<p className="text-[10px] text-gray-500">Recent</p>
						<div className="flex flex-wrap gap-1.5">
							{colorPalette.map((color, index) => (
								<button
									key={index}
									type="button"
									onClick={() => handleColorSelect(color)}
									className={`relative h-7 w-7 rounded-md transition-all ${
										formData.color === color
											? "ring-2 ring-blue-500 ring-offset-1"
											: ""
									}`}
									style={{
										backgroundColor: color,
									}}
								>
									{formData.color === color && (
										<div className="absolute inset-0 flex items-center justify-center">
											<svg
												className="h-3.5 w-3.5 text-white"
												fill="currentColor"
												viewBox="0 0 20 20"
											>
												<path
													fillRule="evenodd"
													d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
													clipRule="evenodd"
												/>
											</svg>
										</div>
									)}
								</button>
							))}
						</div>
					</div>
				</div>
				<div className="flex flex-col gap-4">
					<h3 className="text-sm font-semibold">Conditions</h3>

					{formData.conditions.map((condition) => (
						<div key={condition.id} className="flex flex-col gap-4">
							<div className="space-y-2">
								<Label className="text-xs font-medium">
									Category Condition *
								</Label>
								<Select
									value={condition.categoryCondition}
									onValueChange={(value) =>
										handleConditionChange(
											condition.id,
											"categoryCondition",
											value
										)
									}
								>
									<SelectTrigger className="h-9 w-full text-xs">
										<SelectValue placeholder="Select condition" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="Custom">
											Custom
										</SelectItem>
										<SelectItem value="Standard">
											Standard
										</SelectItem>
										<SelectItem value="Advanced">
											Advanced
										</SelectItem>
									</SelectContent>
								</Select>
							</div>

							<div className="space-y-2">
								<Label className="text-xs font-medium">
									Condition Check *
								</Label>
								<Select
									value={condition.conditionCheck}
									onValueChange={(value) =>
										handleConditionChange(
											condition.id,
											"conditionCheck",
											value
										)
									}
								>
									<SelectTrigger className="h-9 w-full text-xs">
										<SelectValue placeholder="Select check type" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="Information Check">
											Information Check
										</SelectItem>
										<SelectItem value="Registration">
											Registration
										</SelectItem>
										<SelectItem value="Data Check">
											Data Check
										</SelectItem>
									</SelectContent>
								</Select>
							</div>

							<div className="space-y-2">
								<Label className="text-xs font-medium">
									Field *
								</Label>
								<Select
									value={condition.field}
									onValueChange={(value) =>
										handleConditionChange(
											condition.id,
											"field",
											value
										)
									}
								>
									<SelectTrigger className="h-9 w-full text-xs">
										<SelectValue placeholder="Select field" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="Date of Birth">
											Date of Birth
										</SelectItem>
										<SelectItem value="Email">
											Email
										</SelectItem>
										<SelectItem value="Phone">
											Phone
										</SelectItem>
										<SelectItem value="Address">
											Address
										</SelectItem>
									</SelectContent>
								</Select>
							</div>

							<div className="space-y-2">
								<Label className="text-xs font-medium">
									Parameter *
								</Label>
								<Select
									value={condition.parameter}
									onValueChange={(value) =>
										handleConditionChange(
											condition.id,
											"parameter",
											value
										)
									}
								>
									<SelectTrigger className="h-9 w-full text-xs">
										<SelectValue placeholder="Select parameter" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="Before the Date">
											Before the Date
										</SelectItem>
										<SelectItem value="After the Date">
											After the Date
										</SelectItem>
										<SelectItem value="Equals">
											Equals
										</SelectItem>
										<SelectItem value="Contains">
											Contains
										</SelectItem>
									</SelectContent>
								</Select>
							</div>

							<div className="space-y-2">
								<Label className="text-xs font-medium">
									Date *
								</Label>
								<Select
									value={condition.date}
									onValueChange={(value) =>
										handleConditionChange(
											condition.id,
											"date",
											value
										)
									}
								>
									<SelectTrigger className="h-9 w-full text-xs">
										<SelectValue placeholder="Select date" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="01 Jan 1995">
											01 Jan 1995
										</SelectItem>
										<SelectItem value="01 Jan 2000">
											01 Jan 2000
										</SelectItem>
										<SelectItem value="01 Jan 2020">
											01 Jan 2020
										</SelectItem>
										<SelectItem value="01 Jan 2005">
											01 Jan 2005
										</SelectItem>
									</SelectContent>
								</Select>
							</div>
							{formData.conditions.length > 1 && (
								<div className="flex justify-end">
									<button
										type="button"
										onClick={() =>
											handleRemoveCondition(condition.id)
										}
										className="flex items-center gap-2 text-xs text-[#27272A]"
									>
										<CircleMinus className="h-3 w-3" />
										Remove Custom Alert
									</button>
								</div>
							)}
						</div>
					))}

					{formData.conditions.length < 5 && (
						<div className="flex items-center justify-between">
							<div className="flex flex-col gap-0.5">
								<button
									type="button"
									onClick={handleAddCondition}
									className="flex items-center gap-2 text-xs text-[#27272A]"
								>
									<CirclePlus className="h-3 w-3" />
									Add Another Condition
								</button>
								<div className="pl-5 text-[8px] text-gray-500">
									(Max 5)
								</div>
							</div>
						</div>
					)}
				</div>
			</div>
		);
	}

	function renderCategoryFooter() {
		return (
			<div className="flex items-center justify-end gap-3">
				<Button
					variant="outline"
					onClick={handleClose}
					className="h-9 px-4 text-xs"
				>
					Cancel
				</Button>
				<Button
					onClick={handleCategorySubmit}
					disabled={isSubmitting || !formData.name.trim()}
					className="h-9 bg-[#005893] px-4 text-xs hover:bg-[#004a7a]"
				>
					Next
				</Button>
			</div>
		);
	}

	function renderLocationContent() {
		return (
			<LocationSelectionStep
				locationData={locationData}
				onLocationDataChange={setLocationData}
			/>
		);
	}

	function renderLocationFooter() {
		return (
			<div className="flex items-center justify-end gap-3">
				<Button
					variant="outline"
					onClick={handleClose}
					className="h-9 rounded-md border border-gray-200 bg-white px-4 py-2 text-xs font-medium"
				>
					Cancel
				</Button>
				<div className="flex flex-1 items-center justify-end gap-3">
					<Button
						variant="secondary"
						onClick={handleSkipLocationSelection}
						className="h-9 rounded-md bg-gray-100 px-4 py-2 text-xs font-medium"
					>
						Skip for now
					</Button>
					<Button
						onClick={handleLocationSubmit}
						disabled={
							isSubmitting ||
							(!locationData.applyToAll &&
								locationData.selectedLocations.length === 0)
						}
						className="h-9 rounded-md px-4 py-2 text-xs font-medium text-white"
					>
						{isSubmitting ? "Saving..." : "Save"}
					</Button>
				</div>
			</div>
		);
	}

	function renderSuccessContent() {
		const handleDone = () => {
			try {
				const finalData = {
					...formData,
					...locationData,
				};
				onSubmit?.(finalData);
				resetForm();
				onOpenChange(false);
			} catch (error) {
				console.error("Error submitting category:", error);
			}
		};

		return (
			<div className="flex flex-col items-center justify-center gap-8">
				<div className="flex flex-col items-center justify-start gap-11">
					<div className="inline-flex items-center justify-start gap-2.5 overflow-hidden rounded-[500px] bg-zinc-500/5 p-8">
						<div className="relative h-12 w-12 overflow-hidden">
							<Check
								className="absolute top-3 left-2 h-8 w-8 text-[#005893]/16"
								strokeWidth={4}
							/>
						</div>
					</div>

					<div className="flex w-full max-w-72 flex-col items-center justify-start gap-3">
						<div className="text-foreground text-center text-xl leading-loose font-semibold">
							Category Added
						</div>
						<div className="text-muted-foreground text-center text-sm leading-tight font-normal">
							A new category has been added successfully.
						</div>
					</div>
				</div>

				<div className="inline-flex items-start justify-center gap-3">
					<Button
						variant="secondary"
						onClick={() => {
							resetForm();
							setCurrentStep("category");
						}}
						className="h-9 px-4 py-2 text-xs font-medium"
					>
						Add Another Category
					</Button>
					<Button
						onClick={handleDone}
						className="h-9 w-20 bg-sky-800 px-4 py-2 text-xs font-medium text-white hover:bg-sky-900"
					>
						Done
					</Button>
				</div>
			</div>
		);
	}
}
