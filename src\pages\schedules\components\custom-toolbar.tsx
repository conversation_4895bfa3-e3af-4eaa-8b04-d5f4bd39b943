import { FaAngleLeft, FaAngleRight } from "react-icons/fa6";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { IoCalendar } from "react-icons/io5";
import { Button } from "@/components/ui/button";
import clsx from "clsx";
import { getFormattedHeader, VIEW_OPTIONS } from "../utils";
import { Calendar as ShadCalendarUi } from "@/components/ui/calendar"

type CustomToolbarProps = {
    onPrevClick: () => void;
    onNextClick: () => void;
    date: Date;
    view: string;
    setDate: (date: Date) => void;
    setView: (view: string) => void;
    open: boolean;
    setOpen: (open: boolean) => void;
}
export default function CustomToolbar({
    onPrevClick,
    onNextClick,
    date,
    view,
    setDate,
    setView,
    open,
    setOpen
}: CustomToolbarProps) {
    return (
        // Header
        <div className="flex gap-x-20 mb-5">

            <div className="flex items-center w-full">
                <button onClick={onPrevClick}>
                    <FaAngleLeft />
                </button>
                <p className="flex-1 flex items-center justify-center text-center gap-x-2 font-semibold">
                    {getFormattedHeader(date, view)}
                    <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                            <span
                                className="bg-[#F4F4F5] ml-1 p-2 rounded-md"
                            >
                                <IoCalendar />
                            </span>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto overflow-hidden p-0" align="start">
                            <ShadCalendarUi
                                mode="single"
                                selected={date}
                                captionLayout="dropdown"
                                onSelect={(date: Date | undefined) => {
                                    if (!date) return;
                                    setDate(date)
                                    setOpen(false)
                                }}
                            />
                        </PopoverContent>
                    </Popover>
                </p>
                <button onClick={onNextClick}>
                    <FaAngleRight />
                </button>
            </div>

            {/* View Buttons */}
            <div className="w-full bg-[#F4F4F5] py-2 px-2 grid grid-cols-3 gap-x-3">
                {VIEW_OPTIONS.map((option: typeof VIEW_OPTIONS[number]) => (
                    <Button
                        key={option.id}
                        className={clsx("cursor-pointer", view === option.id ? "bg-[#FFF] hover:bg-[#FFF]" : "bg-transparent hover:bg-transparent")}
                        variant="outline"
                        onClick={() => setView(option.id)}
                    >
                        {option.label}
                    </Button>
                ))}
            </div>
        </div>
    );
};