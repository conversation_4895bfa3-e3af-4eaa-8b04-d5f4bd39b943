import { MultiStepSheet } from "@/components/common/MultiStepSheet";
import { CreateStationOptionSelectionStep } from "./station-steps/CreateStationOptionSelectionStep";
import { StationDetailsStep } from "./station-steps/StationDetailsStep";
import { AddServiceProviderStep } from "./station-steps/AddServiceProviderStep";
import { AddFromExistingStep } from "./station-steps/AddFromExistingStep";
import { ImportSettingsStep } from "./station-steps/ImportSettingsStep";
import { StationCompletionStep } from "./station-steps/StationCompletionStep";
import { useStationForm } from "../../hooks/useStationForm";
import { useStepNavigation } from "../../hooks/useStepNavigation";
import { useLocationStationSelection } from "../../hooks/useLocationStationSelection";
import type { CreateStationRequest } from "../../types";
import { useForm } from "react-hook-form";
import type { StationFormData } from "../../schemas/stationSchema";
import { useState } from "react";

interface AddStationSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: CreateStationRequest) => void;
}

export function AddStationSheet({
	open,
	onOpenChange,
	onSubmit,
}: AddStationSheetProps) {
	// Custom hooks
	const {
		formData,
		isSubmitting,
		errors,
		handleMethodToggle,
		handlePreferenceChange,
		validateCurrentStep,
		onFormSubmit,
	} = useStationForm({
		onSubmit: async (data) => {
			await onSubmit?.(data);
			onOpenChange(false);
		},
	});

	// Get form methods from createStationForm
	const form = useForm<StationFormData>({
		defaultValues: {
			stationName: "",
			description: "",
			autoApprove: false,
			serviceVisibility: true,
			serviceAvailability: true,
			availableMethods: ["in-person"],
			stationDuration: 30,
			durationUnit: "minutes",
			applyStationTo: "all-locations",
			selectedStations: [],
		},
		mode: "onChange",
	});

	const { control, watch, setValue, handleSubmit, reset } = form;

	// State to track selected option
	const [selectedOption, setSelectedOption] = useState<
		"new" | "existing" | null
	>(null);

	// State to track selected providers for import
	const [selectedProviders, setSelectedProviders] = useState<any[]>([]);

	// State to track completion info
	const [completionInfo, setCompletionInfo] = useState<{
		providerName?: string;
		stationName?: string;
	}>({});

	// Dynamic total steps based on selected option
	const getTotalSteps = () => {
		if (selectedOption === "existing") return 4; // Option → Add from Existing → Import Settings → Final
		return 4; // Option → Station Details → Add Provider → Final
	};

	const {
		currentStep,
		isLastStep,
		handleNext,
		handlePrevious,
		canProceed,
		goToStep,
	} = useStepNavigation({
		totalSteps: getTotalSteps(),
		initialStep: 1,
	});

	const {
		selectedLocationIds,
		selectedStationIds,
		handleLocationSelection,
		handleStationSelection,
		clearSelections,
	} = useLocationStationSelection();

	// Handle next/submit
	const handleFormNext = () => {
		if (isLastStep) {
			if (selectedOption === "new") {
				handleSubmit(onFormSubmit)();
			} else {
				// Handle completion for "existing" flow
				onOpenChange(false);
			}
		} else {
			handleNext();
		}
	};

	// Handle cancel
	const handleCancel = () => {
		reset();
		clearSelections();
		setSelectedOption(null);
		setSelectedProviders([]);
		setCompletionInfo({});
		onOpenChange(false);
		goToStep(1);
	};

	// Reset everything when sheet closes
	const handleSheetChange = (open: boolean) => {
		if (!open) {
			reset();
			clearSelections();
			setSelectedOption(null);
			setSelectedProviders([]);
			setCompletionInfo({});
			goToStep(1);
		}
		onOpenChange(open);
	};

	// Render step content
	const renderStepContent = () => {
		// Step 1 is always the option selection
		if (currentStep === 1) {
			return (
				<CreateStationOptionSelectionStep
					onAddNewStation={() => {
						setSelectedOption("new");
						handleNext();
					}}
					onAddFromExisting={() => {
						setSelectedOption("existing");
						handleNext();
					}}
				/>
			);
		}

		// Handle different flows based on selected option
		if (selectedOption === "new") {
			switch (currentStep) {
				case 2:
					return (
						<StationDetailsStep
							control={control}
							errors={errors}
							watch={watch}
							setValue={setValue}
							onBack={handlePrevious}
							onAddServiceProvider={handleNext}
						/>
					);
				case 3:
					return (
						<AddServiceProviderStep
							onBack={handlePrevious}
							onSendInvite={(data) => {
								console.log("Service Provider data:", data);
								// Store completion info
								setCompletionInfo({
									providerName: `${data.firstName} ${data.lastName}`,
									stationName:
										watch("stationName") || "New Station",
								});
								// Handle the service provider data here
								handleNext();
							}}
						/>
					);
				case 4:
					return (
						<StationCompletionStep
							onCancel={handleCancel}
							onViewStation={() => {
								console.log("View station clicked");
								// Handle view station navigation
								onOpenChange(false);
							}}
							providerName={
								completionInfo.providerName ||
								watch("stationName") ||
								"New Station"
							}
							isImport={false}
						/>
					);
			}
		} else if (selectedOption === "existing") {
			switch (currentStep) {
				case 2:
					return (
						<AddFromExistingStep
							onBack={handlePrevious}
							onImport={(providers) => {
								console.log("Selected providers:", providers);
								setSelectedProviders(providers);
								handleNext();
							}}
						/>
					);
				case 3:
					return (
						<ImportSettingsStep
							onBack={handlePrevious}
							onImport={(settings) => {
								console.log("Selected settings:", settings);
								// Handle the imported settings
								handleNext();
							}}
							providerName={
								selectedProviders[0]?.name ||
								"Service Provider Station Name"
							}
							locationName="Location Name"
						/>
					);
				case 4:
					return (
						<StationCompletionStep
							onCancel={handleCancel}
							onViewStation={() => {
								console.log("View station clicked");
								// Handle view station navigation
								onOpenChange(false);
							}}
							providerName={
								selectedProviders[0]?.name || "Imported Station"
							}
							isImport={true}
						/>
					);
			}
		}

		return null;
	};

	return (
		<MultiStepSheet
			open={open}
			onOpenChange={handleSheetChange}
			title="Add New Station"
			currentStep={currentStep}
			totalSteps={getTotalSteps()}
			onNext={handleFormNext}
			onPrevious={handlePrevious}
			onCancel={handleCancel}
			canProceed={canProceed(() => true)}
			isSubmitting={isSubmitting}
			showPreviousButton={currentStep > 1 && currentStep !== 4}
			nextButtonText={
				selectedOption === "new" && currentStep === 3
					? "Send Invite"
					: selectedOption === "existing"
						? "Import"
						: currentStep === 4
							? "View Station"
							: "Add Station"
			}
		>
			{renderStepContent()}
		</MultiStepSheet>
	);
}
