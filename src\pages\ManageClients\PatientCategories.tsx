import { useUIStore } from "@/stores/uiStore";
import { useEffect, useCallback, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
	Plus,
	Settings,
	RefreshCcw,
	MessageCircleMore,
	Trash2,
	Pencil,
	Info,
} from "lucide-react";
import { EmptyContent } from "@/components/ui-components/EmptyContent";
import { type Category } from "@/components/ui-components/CategoryListCard";
import { CategoryDetailsSheet } from "@/components/dashboard/category/CategoryDetailsSheet";
import {
	AddCategorySheet,
	type CategoryFormData,
} from "@/components/dashboard/category/AddCategorySheet";
import {
	Pagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";
import { useCategories } from "@/hooks/useCategories";
import { transformCategoriesDataToCategories } from "@/lib/utils/categoryTransformers";

export default function PatientCategories() {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setPageHeaderContent = useUIStore(
		(state) => state.setPageHeaderContent
	);
	const [currentPage, setCurrentPage] = useState(1);
	const categoriesPerPage = 10;

	const {
		data: categoriesData,
		isLoading,
		error,
		refetch,
	} = useCategories({
		page: currentPage,
		limit: categoriesPerPage,
	});

	const categories: Category[] = categoriesData?.data
		? transformCategoriesDataToCategories(categoriesData.data)
		: [];
	const totalPages = categoriesData?.meta?.total_pages || 1;
	const hasCategories = categories.length > 0;

	const [isCategoryDetailsOpen, setIsCategoryDetailsOpen] = useState(false);
	const [selectedCategoryForDetails, setSelectedCategoryForDetails] =
		useState<Category | null>(null);
	const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false);

	const currentCategories = categories;

	const deleteCategoryMutation = {
		onSuccess: () => {
			console.log("Category deleted successfully");
		},
	};

	const handleAddCategory = useCallback(() => {
		console.log("Add Category clicked");
		setIsAddCategoryOpen(true);
	}, []);

	const handleAddCategorySubmit = useCallback(
		(data: CategoryFormData) => {
			console.log("Category submitted:", data);
			setIsAddCategoryOpen(false);
			refetch();
		},
		[refetch]
	);

	const handleOpenCategoryDetails = useCallback((category: Category) => {
		setSelectedCategoryForDetails(category);
		setIsCategoryDetailsOpen(true);
		console.log("Opening category details for:", category.name);
	}, []);

	const handleCloseCategoryDetails = useCallback(() => {
		setIsCategoryDetailsOpen(false);
		setSelectedCategoryForDetails(null);
		console.log("Closed category details");
	}, []);

	const handleSettings = useCallback(() => {
		console.log("Settings clicked");
	}, []);

	const handleRefresh = useCallback(() => {
		console.log("Refresh clicked");
		refetch();
	}, [refetch]);

	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
		console.log("Changed to page:", page);
	}, []);

	const handlePreviousPage = useCallback(() => {
		if (currentPage > 1) {
			handlePageChange(currentPage - 1);
		}
	}, [currentPage, handlePageChange]);

	const handleNextPage = useCallback(() => {
		if (currentPage < totalPages) {
			handlePageChange(currentPage + 1);
		}
	}, [currentPage, totalPages, handlePageChange]);

	const handleEditCategory = useCallback((category: Category) => {
		console.log("Edit category:", category.id);
	}, []);

	const handleDeleteCategory = useCallback((category: Category) => {
		console.log("Delete category:", category.id);
	}, []);

	const handleViewCategory = useCallback((category: Category) => {
		console.log("View category:", category.id);
	}, []);

	const handleInfoCategory = useCallback((category: Category) => {
		console.log("Info category:", category.id);
	}, []);

	useEffect(() => {
		setBreadcrumbs([
			{ label: "Dashboard", href: "/" },
			{ label: "Patients", href: "/dashboard/patients" },
			{
				label: "Patient Categories",
				href: "/dashboard/patients/categories",
			},
		]);

		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				<div>
					<h1 className="text-foreground text-2xl font-bold">
						List of Categories
					</h1>
				</div>
				<div className="flex items-center space-x-3">
					<Button
						variant="outline"
						size="icon"
						onClick={handleSettings}
						className="h-9 w-9"
					>
						<Settings className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-9 w-9"
						onClick={handleRefresh}
						disabled={isLoading}
					>
						<RefreshCcw className="h-4 w-4" />
					</Button>
					<Button
						variant="default"
						onClick={handleAddCategory}
						className="h-9 bg-[#005893] px-4 text-white hover:bg-[#004a7a]"
					>
						<Plus className="h-4 w-4" />
						Add Category
					</Button>
				</div>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setBreadcrumbs([]);
			setPageHeaderContent(null);
		};
	}, [
		setBreadcrumbs,
		setPageHeaderContent,
		handleSettings,
		handleAddCategory,
	]);

	if (isLoading) {
		return (
			<div className="flex min-h-[400px] items-center justify-center">
				<div className="text-center">
					<div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-[#005893]"></div>
					<p className="text-gray-500">Loading categories...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex min-h-[400px] items-center justify-center">
				<div className="text-center">
					<p className="mb-4 text-red-500">
						Failed to load categories
					</p>
					<Button onClick={() => refetch()} variant="outline">
						Try Again
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-">
			{!hasCategories ? (
				<div className="flex min-h-[400px] items-center justify-center">
					<EmptyContent
						title="No categories added"
						description="You haven't added any categories yet. Add one to get started."
						actions={[
							{
								label: "Add New Categories",
								onClick: handleAddCategory,
								variant: "primary",
							},
						]}
					/>
				</div>
			) : (
				<div>
					<div className="mt-2 overflow-hidden rounded-xl border border-[#E4E4E7]">
						<table className="w-full">
							<thead>
								<tr className="h-12 border-b border-gray-200">
									<th className="w-14 px-3 text-left">
										<div className="text-xs font-medium text-[#71717A]">
											Color
										</div>
									</th>
									<th
										className="px-3 text-left"
										style={{ width: "auto" }}
									>
										<div className="text-xs font-medium text-[#71717A]">
											Category Name
										</div>
									</th>
									<th className="w-28 px-3 text-left">
										<div className="text-xs font-medium text-[#71717A]">
											Status
										</div>
									</th>
									<th className="w-44 px-3 text-left">
										<div className="text-xs font-medium text-[#71717A]">
											Clients
										</div>
									</th>
									<th className="w-44 px-3 text-left">
										<div className="text-xs font-medium text-[#71717A]">
											Stations
										</div>
									</th>
									<th className="w-48 px-3 text-left">
										<div className="text-xs font-medium text-[#71717A]">
											Date Added
										</div>
									</th>
								</tr>
							</thead>
							<tbody>
								{currentCategories.map((category) => (
									<tr
										key={category.id}
										onClick={() =>
											handleOpenCategoryDetails(category)
										}
										className="h-16 cursor-pointer border-t border-gray-200 bg-white transition-colors hover:bg-gray-50"
									>
										<td className="w-14 px-3">
											<div className="flex items-center justify-start">
												<div
													className="h-3 w-3 rounded-full"
													style={{
														backgroundColor:
															category.color,
													}}
												/>
											</div>
										</td>
										<td
											className="px-3"
											style={{ width: "auto" }}
										>
											<div className="text-sm leading-tight font-normal text-gray-900">
												{category.name}
											</div>
										</td>
										<td className="w-28 px-3">
											<div
												className={`inline-flex items-center justify-center gap-2.5 rounded-md px-2 py-1 ${
													category.status === "Active"
														? "bg-green-100 text-green-800"
														: "bg-gray-100 text-gray-600"
												}`}
											>
												<div className="text-[10px] leading-3 font-medium">
													{category.status}
												</div>
											</div>
										</td>
										<td className="w-44 px-3">
											<div className="inline-flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1">
												<div className="text-[10px] leading-3 font-medium text-gray-900">
													{category.clients}
												</div>
											</div>
										</td>
										<td className="w-44 px-3">
											<div className="inline-flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1">
												<div className="text-[10px] leading-3 font-medium text-gray-900">
													{category.stations}
												</div>
											</div>
										</td>
										<td className="w-48 px-3">
											<div className="text-xs leading-none font-normal text-gray-500">
												{category.dateAdded}
											</div>
										</td>
										<td className="w-16 px-3">
											<div className="flex items-center justify-end gap-1.5">
												<button
													onClick={(e) => {
														e.stopPropagation();
														handleViewCategory(
															category
														);
													}}
													className="flex h-6 w-6 items-center justify-center rounded-md border border-gray-200 bg-white p-2 transition-colors hover:bg-gray-50"
												>
													<MessageCircleMore className="h-3 w-3 text-[#71717A]" />
												</button>
												<button
													onClick={(e) => {
														e.stopPropagation();
														handleDeleteCategory(
															category
														);
													}}
													className="flex h-6 w-6 items-center justify-center rounded-md border border-gray-200 bg-white p-2 transition-colors hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
												>
													<Trash2 className="h-3 w-3 text-[#71717A]" />
												</button>
												<button
													onClick={(e) => {
														e.stopPropagation();
														handleEditCategory(
															category
														);
													}}
													className="flex h-6 w-6 items-center justify-center rounded-md border border-gray-200 bg-white p-2 transition-colors hover:bg-gray-50"
												>
													<Pencil className="h-3 w-3 text-[#71717A]" />
												</button>
												<button
													onClick={(e) => {
														e.stopPropagation();
														handleInfoCategory(
															category
														);
													}}
													className="flex h-6 w-6 items-center justify-center rounded-md border border-gray-200 bg-white p-2 transition-colors hover:bg-gray-50"
												>
													<Info className="h-3 w-3 text-[#71717A]" />
												</button>
											</div>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
					<div className="mt-4 flex justify-end">
						<div>
							<Pagination>
								<PaginationContent>
									<PaginationItem>
										<PaginationPrevious
											onClick={handlePreviousPage}
											className={
												currentPage === 1
													? "pointer-events-none opacity-50"
													: "cursor-pointer"
											}
										/>
									</PaginationItem>
									{Array.from(
										{ length: totalPages },
										(_, i) => i + 1
									).map((page) => (
										<PaginationItem key={page}>
											<PaginationLink
												onClick={() =>
													handlePageChange(page)
												}
												isActive={currentPage === page}
												className="cursor-pointer"
											>
												{page}
											</PaginationLink>
										</PaginationItem>
									))}

									{totalPages > 5 &&
										currentPage < totalPages - 2 && (
											<PaginationItem>
												<PaginationEllipsis />
											</PaginationItem>
										)}

									<PaginationItem>
										<PaginationNext
											onClick={handleNextPage}
											className={
												currentPage === totalPages
													? "pointer-events-none opacity-50"
													: "cursor-pointer"
											}
										/>
									</PaginationItem>
								</PaginationContent>
							</Pagination>
						</div>
					</div>
				</div>
			)}
			<CategoryDetailsSheet
				open={isCategoryDetailsOpen}
				onClose={handleCloseCategoryDetails}
				category={selectedCategoryForDetails}
			/>
			<AddCategorySheet
				open={isAddCategoryOpen}
				onOpenChange={setIsAddCategoryOpen}
				onSubmit={handleAddCategorySubmit}
			/>
		</div>
	);
}
