import { apiClient } from "./clients";

const CATEGORIES_ENDPOINTS = {
	base: "/core/api/v1/categories",
	byId: (id: string | number) => `/core/api/v1/categories/${id}`,
} as const;

export interface CategoryCondition {
	id: number;
	attribute: {
		id: number;
		name: string;
		type: string;
	};
	operator: {
		key: string;
		label: string;
	};
	value: any;
}

export interface CategoryData {
	id: number;
	name: string;
	color: string;
	is_conditional: boolean;
	description: string;
	type: string;
	client_count: number;
	station_count: number;
	is_active: boolean;
	business_id: number;
	conditions?: CategoryCondition[];
	created_at?: string;
	updated_at?: string;
}

export interface CategoriesFilters {
	page?: number;
	limit?: number;
	search?: string;
	status?: string;
}

export interface CategoriesMeta {
	total: number;
	page: number;
	per_page: number;
	total_pages: number;
}

export interface CategoriesResponse {
	success: boolean;
	message: string;
	data: CategoryData[];
	meta: CategoriesMeta;
}

export interface CategoryDetailResponse {
	success: boolean;
	message: string;
	data: CategoryData;
}

export interface CreateCategoryRequest {
	name: string;
	description: string;
	color: string;
	is_conditional?: boolean;
	type?: string;
	conditions?: Omit<CategoryCondition, 'id'>[];
}

export interface CreateCategoryResponse {
	success: boolean;
	message: string;
	data: CategoryData;
}

export interface UpdateCategoryRequest {
	name?: string;
	description?: string;
	color?: string;
	is_conditional?: boolean;
	is_active?: boolean;
	conditions?: Omit<CategoryCondition, 'id'>[];
}

export interface UpdateCategoryResponse {
	success: boolean;
	message: string;
	data: CategoryData;
}

export const categoriesApi = {
	getCategories: async (
		filters: CategoriesFilters = {}
	): Promise<CategoriesResponse> => {
		const params = new URLSearchParams();
		if (filters.page !== undefined) {
			params.append("page", filters.page.toString());
		}
		if (filters.limit !== undefined) {
			params.append("limit", filters.limit.toString());
		}
		if (filters.search) {
			params.append("search", filters.search);
		}
		if (filters.status) {
			params.append("status", filters.status);
		}
		const queryString = params.toString();
		const url = queryString
			? `${CATEGORIES_ENDPOINTS.base}?${queryString}`
			: CATEGORIES_ENDPOINTS.base;

		const response = await apiClient.get(url);
		return response.data;
	},	
};
