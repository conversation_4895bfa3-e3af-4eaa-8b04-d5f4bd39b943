import React, { useState, use<PERSON><PERSON>back } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { X, Edit, Trash2, Info } from "lucide-react";
import { type Category } from "@/components/ui-components/CategoryListCard";
import {
	Pagin<PERSON>,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";

interface Client {
	id: string;
	name: string;
	email: string;
	phone: string;
	status: "Active" | "Inactive";
	initials: string;
}

interface CategoryDetailsSheetProps {
	open: boolean;
	onClose: () => void;
	category?: Category | null;
}

export const CategoryDetailsSheet: React.FC<CategoryDetailsSheetProps> = ({
	open,
	onClose,
	category,
}) => {
	const [activeTab, setActiveTab] = useState("information");
	const [clients] = useState<Client[]>([
		{
			id: "1",
			name: "<PERSON><PERSON>",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Active",
			initials: "<PERSON>",
		},
		{
			id: "2",
			name: "<PERSON>. <PERSON> <PERSON>",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Active",
			initials: "AL",
		},
		{
			id: "3",
			name: "Dr. <PERSON> <PERSON>",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Inactive",
			initials: "AL",
		},
		{
			id: "4",
			name: "Dr. <PERSON> <PERSON>",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Inactive",
			initials: "AL",
		},
		{
			id: "5",
			name: "Dr. Abraham Lincoln",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Inactive",
			initials: "AL",
		},
		{
			id: "6",
			name: "Dr. Abraham Lincoln",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Active",
			initials: "AL",
		},
		{
			id: "7",
			name: "Dr. Abraham Lincoln",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Active",
			initials: "AL",
		},
		{
			id: "7",
			name: "Dr. Abraham Lincoln",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Active",
			initials: "AL",
		},
		{
			id: "7",
			name: "Dr. Abraham Lincoln",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Active",
			initials: "AL",
		},
		{
			id: "7",
			name: "Dr. Abraham Lincoln",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Active",
			initials: "AL",
		},
		{
			id: "7",
			name: "Dr. Abraham Lincoln",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Active",
			initials: "AL",
		},
		{
			id: "7",
			name: "Dr. Abraham Lincoln",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Active",
			initials: "AL",
		},
		{
			id: "7",
			name: "Dr. Abraham Lincoln",
			email: "<EMAIL>",
			phone: "+****************",
			status: "Active",
			initials: "AL",
		},
	]);

	const [currentPage, setCurrentPage] = useState(1);
	const clientsPerPage = 7;
	const totalPages = Math.ceil(clients.length / clientsPerPage);
	const startIndex = (currentPage - 1) * clientsPerPage;
	const endIndex = startIndex + clientsPerPage;
	const currentClients = clients.slice(startIndex, endIndex);

	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
	}, []);

	const handlePreviousPage = useCallback(() => {
		if (currentPage > 1) {
			handlePageChange(currentPage - 1);
		}
	}, [currentPage, handlePageChange]);

	const handleNextPage = useCallback(() => {
		if (currentPage < totalPages) {
			handlePageChange(currentPage + 1);
		}
	}, [currentPage, totalPages, handlePageChange]);

	if (!category) return null;

	const handleEdit = () => {
		console.log("Edit category:", category.id);
	};

	const locationData = [
		{
			name: "Location Name 1",
			stations: [
				"Station Name",
				"Station Name",
				"Station Name",
				"Station Name",
				"Station Name",
			],
		},
		{
			name: "Location Name 2",
			stations: [
				"Station Name",
				"Station Name",
				"Station Name",
				"Station Name",
				"Station Name",
			],
		},
		{
			name: "Location Name 3",
			stations: [
				"Station Name",
				"Station Name",
				"Station Name",
				"Station Name",
				"Station Name",
			],
		},
	];

	const tabItems = [
		{ value: "information", label: "Information" },
		{ value: "clients", label: "Clients" },
	];

	return (
		<Sheet open={open} onOpenChange={onClose}>
			<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
				<div className="flex h-full flex-col gap-6">
					<div className="flex items-start justify-between gap-2.5">
						<div className="flex flex-1 flex-col items-start justify-start gap-2.5">
							<div className="flex items-center justify-start gap-2.5">
								<div className="text-base leading-7 font-semibold text-gray-900">
									{category.name}
								</div>
								<div
									className="h-3.5 w-3.5 rounded-full"
									style={{ backgroundColor: category.color }}
								/>
							</div>
							<div className="text-xs leading-none font-normal text-gray-500">
								Category Description Category Description
								Category Description Category Description
								Category Description
							</div>
						</div>
						<div className="flex items-start justify-start gap-2.5">
							<Button
								variant="ghost"
								size="icon"
								onClick={onClose}
								className="h-9 w-9 rounded-md"
							>
								<X className="h-4 w-4" />
							</Button>
						</div>
					</div>

					<div className="flex h-10 items-center justify-start rounded-lg bg-gray-100 p-1">
						{tabItems.map((tab) => (
							<button
								key={tab.value}
								onClick={() => setActiveTab(tab.value)}
								className={`flex h-8 flex-1 items-center justify-center gap-2 rounded-md px-3 py-1 ${
									activeTab === tab.value
										? "bg-white text-gray-900 shadow-sm"
										: "text-gray-600"
								}`}
							>
								<div
									className={`text-center text-xs leading-none ${
										activeTab === tab.value
											? "font-semibold"
											: "font-medium"
									}`}
								>
									{tab.label}
								</div>
							</button>
						))}
					</div>

					<div className="flex flex-1 flex-col gap-6 overflow-y-auto">
						{activeTab === "information" && (
							<>
								<div className="flex flex-col items-start justify-start gap-2">
									<div className="text-sm leading-tight font-semibold text-gray-900">
										Condition Info
									</div>
									<div className="flex w-full items-start justify-start">
										<div className="flex flex-1 flex-col items-start justify-start">
											<div className="flex w-full flex-col items-start justify-center gap-1 border-t border-gray-200 py-3">
												<div className="text-[10px] leading-3 font-normal text-gray-500">
													Category Type
												</div>
												<div className="text-sm leading-tight font-normal text-gray-900">
													Custom Conditions
												</div>
											</div>
										</div>
									</div>
								</div>

								<div className="flex flex-col items-start justify-start gap-2">
									<div className="text-sm leading-tight font-semibold text-gray-900">
										Applied To
									</div>
									<div className="flex w-full items-start justify-start">
										<div className="flex flex-1 flex-col items-start justify-start">
											{locationData.map(
												(location, index) => (
													<div
														key={index}
														className="flex w-full flex-col items-start justify-center gap-1 border-t border-gray-200 py-3"
													>
														<div className="text-sm leading-tight font-normal text-gray-900">
															{location.name}
														</div>
														<div className="flex w-full flex-wrap items-start justify-start gap-2">
															{location.stations.map(
																(
																	station,
																	stationIndex
																) => (
																	<div
																		key={
																			stationIndex
																		}
																		className="flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1"
																	>
																		<div className="text-[10px] leading-3 font-medium text-gray-900">
																			{
																				station
																			}
																		</div>
																	</div>
																)
															)}
														</div>
													</div>
												)
											)}
										</div>
									</div>
								</div>
							</>
						)}

						{activeTab === "clients" && (
							<div className="flex flex-1 flex-col gap-4 overflow-hidden">
								<div className="flex-1 overflow-y-auto">
									<div className="flex flex-col items-start justify-start gap-1 self-stretch rounded-xl border border-gray-200">
										{currentClients.map((client, index) => (
											<div
												key={client.id}
												className={`bg-Foreground-Base inline-flex items-center justify-start self-stretch ${
													index > 0
														? "border-t border-gray-200"
														: ""
												}`}
											>
												<div className="flex w-72 min-w-20 items-start justify-start gap-3 self-stretch px-3 pt-3.5 pb-3">
													<div className="relative h-9 w-9 overflow-hidden rounded-full bg-gray-200">
														<div className="absolute top-[8px] left-[8px] h-5 w-5 justify-center text-center text-xs leading-none font-medium text-gray-600">
															{client.initials}
														</div>
													</div>
													<div className="inline-flex w-56 flex-col items-start justify-start gap-1">
														<div className="inline-flex items-center justify-start gap-1 self-stretch">
															<div className="justify-center text-sm leading-tight font-medium text-gray-900">
																{client.name}
															</div>
															<div className="h-1.5 w-1.5 rounded-full bg-red-500" />
														</div>
														<div className="justify-center text-xs leading-none font-normal text-gray-500">
															{client.email}
														</div>
														<div className="justify-center text-xs leading-none font-normal text-gray-500">
															{client.phone}
														</div>
													</div>
												</div>

												<div className="flex min-w-20 flex-1 items-start justify-start gap-3 self-stretch px-3 pt-3.5 pb-3">
													<div
														className={`flex items-center justify-center gap-2.5 rounded-md px-2 py-1 ${
															client.status ===
															"Active"
																? "bg-green-100"
																: "bg-gray-200"
														}`}
													>
														<div className="justify-start text-[10px] leading-3 font-medium text-gray-900">
															{client.status}
														</div>
													</div>
												</div>
												<div className="flex min-w-16 items-start justify-end gap-1.5 self-stretch px-3 pt-3.5 pb-3">
													<button className="flex h-6 w-6 items-center justify-center gap-2 rounded-md bg-gray-100 p-2 transition-colors hover:bg-gray-200">
														<Trash2 className="h-3 w-3 text-gray-500" />
													</button>
													<button className="flex h-6 w-6 items-center justify-center gap-2 rounded-md bg-gray-100 p-2 transition-colors hover:bg-gray-200">
														<Info className="h-3 w-3 text-gray-500" />
													</button>
												</div>
											</div>
										))}
									</div>
								</div>

								{totalPages > 1 && (
									<div className="flex justify-end pt-4">
										<div>
											<Pagination>
												<PaginationContent>
													<PaginationItem>
														<PaginationPrevious
															onClick={
																handlePreviousPage
															}
															className={
																currentPage ===
																1
																	? "pointer-events-none opacity-50"
																	: "cursor-pointer"
															}
														/>
													</PaginationItem>
													{Array.from(
														{ length: totalPages },
														(_, i) => i + 1
													).map((page) => (
														<PaginationItem
															key={page}
														>
															<PaginationLink
																onClick={() =>
																	handlePageChange(
																		page
																	)
																}
																isActive={
																	currentPage ===
																	page
																}
																className="cursor-pointer"
															>
																{page}
															</PaginationLink>
														</PaginationItem>
													))}

													{totalPages > 5 &&
														currentPage <
															totalPages - 2 && (
															<PaginationItem>
																<PaginationEllipsis />
															</PaginationItem>
														)}

													<PaginationItem>
														<PaginationNext
															onClick={
																handleNextPage
															}
															className={
																currentPage ===
																totalPages
																	? "pointer-events-none opacity-50"
																	: "cursor-pointer"
															}
														/>
													</PaginationItem>
												</PaginationContent>
											</Pagination>
										</div>
									</div>
								)}
							</div>
						)}
					</div>

					<div className="flex items-center justify-end gap-3">
						<Button
							variant="outline"
							onClick={onClose}
							className="h-9"
						>
							Close
						</Button>
						<Button onClick={handleEdit} className="h-9">
							<Edit className="mr-2 h-4 w-4" />
							Edit
						</Button>
					</div>
				</div>
			</SheetContent>
		</Sheet>
	);
};
