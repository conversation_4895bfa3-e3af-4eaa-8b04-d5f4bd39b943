import calendarImage from "@/pages/schedules/assets/calendar.png";
import { MdOutlineLocationCity } from "react-icons/md";
import { CiLocationOn } from "react-icons/ci";
import { FaCalendar, FaClock } from "react-icons/fa6";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function AppointmentScheduled() {
    return (
        <div>
            <div className="mt-7 flex flex-col items-center justify-center">
                <img src={calendarImage} alt="Calendar" className="scale-80" />
                <h1 className="text-3xl font-medium">
                    Appointment Scheduled
                </h1>
                <p className="mt-7 text-xl font-light">
                    Appointment has been successfully scheduled.
                </p>
                <div className="w-full border border-[#E4E4E7] py-3 px-5 mt-9 rounded-lg flex justify-between">
                    <div className="flex gap-x-4">
                        <div className="shrink-0 size-12 bg-[#E4E4E7] text-[#A1A1AA] rounded-full grid place-content-center">
                            AB
                        </div>
                        <div>
                            <h1 className="mb-1.5"><PERSON></h1>
                            <p className="text-[#71717A] font-light text-sm mb-5"><EMAIL></p>
                            <div className="flex gap-x-3 mb-5">
                                <div className="bg-[#F4F4F5] py-2 px-2 text-sm rounded-md font-medium">Dr. Adam Sowenson</div>
                                <div className="bg-[#F4F4F5] py-2 px-2 text-sm rounded-md font-medium">Blood Test</div>
                            </div>
                            <p className="flex items-start gap-x-2 mb-3">
                                <MdOutlineLocationCity color="#A1A1AA" size={20} />
                                <span className="text-[#1F1F1F] text-[15px] font-light">[Location Name]</span>
                            </p>
                            <p className="flex items-start gap-x-2">
                                <CiLocationOn color="#A1A1AA" size={20} className="shrink-0" />
                                <span className="text-[#1F1F1F] text-[15px] font-light">
                                    Dr. Adam200 University Ave W, Waterloo, ON N2L 3G1, Canada Sowenson
                                </span>
                            </p>
                        </div>
                    </div>
                    <div>
                        <h1 className="font-medium text-end">#UID</h1>
                        <div className="flex gap-x-2">
                            <p className="text-nowrap text-xs text-[#27272A] opacity-60 flex items-center gap-x-1">
                                <FaCalendar />
                                12 Apr 2025
                            </p>
                            <p className="text-nowrap text-xs text-[#27272A] opacity-60 flex items-center gap-x-1">
                                <FaClock />
                                09:15 AM - 9:30 AM
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div className="flex items-center justify-end gap-x-3 mt-5">
                <Button variant="outline" className="cursor-pointer bg-[#F4F4F5] py-5">
                    Cancel Appointment
                </Button>
                <Button className="cursor-pointer py-5">
                    Add Another Appointment
                </Button>
            </div>
        </div>
    )
}