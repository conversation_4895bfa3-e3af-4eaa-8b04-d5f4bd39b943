/**
 * Business Attributes API Types
 * Based on the endpoint: GET /core/api/v1/business-attributes
 */

export interface BusinessAttribute {
  id: number;
  key: string;
  label: string;
  type: 'dropdown' | 'text' | 'number' | 'date' | 'checkbox' | 'textarea';
  is_required: boolean;
  is_basic_attribute: boolean;
  is_system_field: boolean;
  show_in_list: boolean;
  import_column_name: string;
  field_config_id: number;
  emr_sync_id: number | null;
  is_validator: boolean;
  business_id: number;
}

export interface BusinessAttributesMeta {
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

export interface BusinessAttributesResponse {
  success: boolean;
  message: string;
  data: BusinessAttribute[];
  meta: BusinessAttributesMeta;
}

export interface BusinessAttributesFilters {
  page?: number;
  per_page?: number;
  search?: string;
  type?: BusinessAttribute['type'];
  is_required?: boolean;
  show_in_list?: boolean;
}

export interface BusinessAttributesError {
  success: false;
  message: string;
  errors?: string[];
}
