import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import {
    Sheet,
    Sheet<PERSON>ontent,
    SheetDescription,
    She<PERSON><PERSON>ooter,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Sheet<PERSON>rigger,
} from "@/components/ui/sheet"
import { zodResolver } from "@hookform/resolvers/zod"
import { LuSettings2 } from "react-icons/lu"
import { filterSchema } from "../schema/filter"
import type { z } from "zod"
import { useForm } from "react-hook-form"
import { FiUser } from "react-icons/fi";
import { GoCheckCircleFill } from "react-icons/go";
import { PiSpeakerHighThin } from "react-icons/pi";
import { MdOutlineVideocam } from "react-icons/md";
import { DatePicker } from "@/components/common/Datepicker/DatePicker"
import { RefactorMultiSelect } from "../components/custom-select"
import { locations, providers, services } from "../db"

export default function FilterAppointment() {
    const form = useForm<z.infer<typeof filterSchema>>({
        resolver: zodResolver(filterSchema),
        defaultValues: {
            location: [],
            providers: [],
            services: [],
            appointmentType: "in-person",
            dateRange: "",
        }
    })

    return (
        <Sheet>
            <SheetTrigger asChild>
                <Button variant="outline" className="py-5 cursor-pointer">
                    <LuSettings2 />
                </Button>
            </SheetTrigger>
            <SheetContent className="z-[1003] py-5 px-2 sm:max-w-[610px]">
                <SheetHeader>
                    <SheetTitle className="text-2xl mb-1">Filter Appointments</SheetTitle>
                    <SheetDescription>Select options below to help filter your search</SheetDescription>
                </SheetHeader>
                <div className="px-4 space-y-7">

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="location" className="text-[#18181B] text-base">Location</Label>

                        <RefactorMultiSelect
                            value={form.watch("location")}
                            setValue={(value) => form.setValue("location", value as string[])}
                            placeholder="Select Location"
                            label="Location"
                            id="location"
                            options={locations}
                        />
                    </div>

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="location" className="text-[#18181B] text-base">Providers</Label>

                        <RefactorMultiSelect
                            value={form.watch("providers")}
                            setValue={(value) => form.setValue("providers", value as string[])}
                            placeholder="Select Providers"
                            label="Providers"
                            id="providers"
                            options={providers}
                        />
                    </div>

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="location" className="text-[#18181B] text-base">Services</Label>

                        <RefactorMultiSelect
                            value={form.watch("services")}
                            setValue={(value) => form.setValue("services", value as string[])}
                            placeholder="Select Services"
                            label="Services"

                            options={services}
                        />
                    </div>

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="appointmentType" className="text-[#18181B] text-base">Appointment Method</Label>

                        <div className="grid grid-cols-3 gap-2">
                            {["in-person", "audio", "video"].map((type, index) => (
                                <button
                                    type="button"
                                    key={index}
                                    className={`cursor-pointer border flex items-center justify-between py-3 px-4 rounded-xl font-regular opacity-100 gap-x-3 text-center ${form.watch("appointmentType") === type ? "border-[#005893] bg-[#0058931A] text-[#005893]" : "border-[#D4D4D8]"}`}
                                    onClick={() => form.setValue("appointmentType", type as "in-person" | "audio" | "video")}
                                >
                                    {type === "in-person" && <FiUser color="#005893" className="text-xl" />}
                                    {type === "audio" && <PiSpeakerHighThin color="#005893" className="text-xl" />}
                                    {type === "video" && <MdOutlineVideocam color="#005893" className="text-xl" />}
                                    <span>{type.charAt(0).toUpperCase() + type.slice(1)}</span>
                                    <GoCheckCircleFill color="#005893" />
                                </button>
                            ))}
                        </div>

                    </div>

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="dateRange" className="text-[#18181B] text-base">
                            Select a Date or Range
                        </Label>

                        <div className="w-full">
                            <style>
                                {`
                                div[data-radix-popper-content-wrapper] {
                                    z-index: 3000 !important;
                                }
                                `}
                            </style>
                            <DatePicker
                                onChange={() => { }}
                                placeholder="Pick a date"
                                size="md"
                                variant="default"
                            />
                        </div>
                    </div>
                </div>
                <SheetFooter>
                    <div className="flex items-center justify-between">
                        <Button
                            variant={"ghost"}
                            className="cursor-pointer opacity-60 font-medium"
                            type="button"
                            onClick={() => form.reset()}
                        >
                            Reset
                        </Button>
                        <div className="space-x-4">
                            <Button variant="outline" className="cursor-pointer py-5">
                                Cancel
                            </Button>
                            <Button className="cursor-pointer py-5">
                                Apply
                            </Button>
                        </div>
                    </div>
                </SheetFooter>
            </SheetContent>
        </Sheet>
    )
}