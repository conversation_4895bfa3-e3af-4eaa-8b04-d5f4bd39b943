import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./styles/index.css";
import { RouterProvider } from "react-router";
import { QueryProvider } from "./providers/QueryProvider";
import { ErrorBoundary } from "./components/common/ErrorBoundary";
import { router } from "./routes";
import 'react-big-calendar/lib/css/react-big-calendar.css';

createRoot(document.getElementById("root")!).render(
	<StrictMode>
		<ErrorBoundary
			onError={(error, errorInfo) => {
				// You can integrate with error reporting services here
				// Example: logErrorToService(error, errorInfo);
				console.error("Application Error:", error, errorInfo);
			}}
		>
			<QueryProvider>
				<RouterProvider router={router} />
			</QueryProvider>
		</ErrorBoundary>
	</StrictMode>
);

