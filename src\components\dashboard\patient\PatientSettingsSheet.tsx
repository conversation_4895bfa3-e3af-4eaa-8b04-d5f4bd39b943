import { useState } from "react";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

interface PatientSettingsSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSave?: (settings: PatientSettings) => void;
}

export interface PatientSettings {
	validationEnabled: boolean;
	validationFields: {
		field1: string;
		field2: string;
	};
	verificationEnabled: boolean;
}

export function PatientSettingsSheet({
	open,
	onOpenChange,
	onSave,
}: PatientSettingsSheetProps) {
	const [settings, setSettings] = useState<PatientSettings>({
		validationEnabled: true,
		validationFields: {
			field1: "",
			field2: "",
		},
		verificationEnabled: false,
	});

	const handleValidationToggle = (checked: boolean) => {
		setSettings({
			...settings,
			validationEnabled: checked,
		});
	};

	const handleVerificationToggle = (checked: boolean) => {
		setSettings({
			...settings,
			verificationEnabled: checked,
		});
	};

	const handleFieldChange = (field: "field1" | "field2", value: string) => {
		setSettings({
			...settings,
			validationFields: {
				...settings.validationFields,
				[field]: value,
			},
		});
	};

	const handleSave = () => {
		onSave?.(settings);
		onOpenChange(false);
	};

	const handleClose = () => {
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
				<div className="flex h-full flex-col justify-between">
					<div className="space-y-2">
						<div className="border-b border-gray-200 pb-3">
							<h2 className="text-base font-semibold text-[#1F1F1F]">
								Patient Validation Settings
							</h2>
						</div>
						<div className="space-y-3 border-b border-[#E4E4E7] pb-5">
							<div className="flex items-start justify-between border-b py-2">
								<div className="space-y-1">
									<div className="flex items-center gap-4">
										<div className="flex items-center gap-2">
											<span className="text-[13px] font-medium text-[#1F1F1F]">
												Enable Patients Validation
											</span>
										</div>
									</div>
									<p className="max-w-[409px] truncate text-[11px] text-[#1F1F1F]">
										This will enable the validation process.
										Patients who are not validated will not
										be allowed to continue.
									</p>
								</div>
								<div className="flex items-center gap-1.5">
									<Switch
										checked={settings.validationEnabled}
										onCheckedChange={handleValidationToggle}
									/>
									<span className="text-xs text-gray-500">
										{settings.validationEnabled
											? "On"
											: "Off"}
									</span>
								</div>
							</div>
							<div className="space-y-6">
								<h3 className="text-[13px] font-bold text-[#1F1F1F]">
									Select Titles for Validation
								</h3>

								<div className="space-y-4">
									<div className="w-full space-y-2">
										<label className="text-xs font-medium">
											Validation Field 1
										</label>
										<Select
											value={
												settings.validationFields.field1
											}
											onValueChange={(value) =>
												handleFieldChange(
													"field1",
													value as string
												)
											}
											// className="w-full"
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select Field" />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="firstName">
													First Name
												</SelectItem>
												<SelectItem value="lastName">
													Last Name
												</SelectItem>
												<SelectItem value="email">
													Email
												</SelectItem>
												<SelectItem value="phone">
													Phone Number
												</SelectItem>
												<SelectItem value="dob">
													Date of Birth
												</SelectItem>
											</SelectContent>
										</Select>
									</div>
									<div className="w-full space-y-2">
										<label className="text-xs font-medium">
											Validation Field 2
										</label>
										<Select
											value={
												settings.validationFields.field2
											}
											onValueChange={(value) =>
												handleFieldChange(
													"field2",
													value as string
												)
											}
											// className="w-full"
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select Field" />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="firstName">
													First Name
												</SelectItem>
												<SelectItem value="lastName">
													Last Name
												</SelectItem>
												<SelectItem value="email">
													Email
												</SelectItem>
												<SelectItem value="phone">
													Phone Number
												</SelectItem>
												<SelectItem value="dob">
													Date of Birth
												</SelectItem>
											</SelectContent>
										</Select>
									</div>
								</div>
							</div>
						</div>
						<div className="space-y-2">
							<div className="border-b border-[#E4E4E7] pb-3">
								<h2 className="text-base font-semibold text-[#1F1F1F]">
									Patient Verification
								</h2>
							</div>
							<div className="flex items-start justify-between border-b border-[#F5F5F5] py-2">
								<div className="space-y-1">
									<div className="flex items-center gap-4">
										<div className="flex items-center gap-2">
											<span className="text-xs font-medium text-[#1F1F1F]">
												Enable Patients Verification
											</span>
										</div>
									</div>
									<p className="max-w-[409px] truncate text-[11px] text-[#1F1F1F]">
										This will enable the verification step
										to identify new or existing Patients.
										Users who are not registered will be
										redirected to the link / form pasted in
										the response section based on selection.
									</p>
								</div>
								<div className="flex items-center gap-1.5">
									<Switch
										checked={settings.verificationEnabled}
										onCheckedChange={
											handleVerificationToggle
										}
									/>
									<span className="text-xs text-gray-500">
										{settings.verificationEnabled
											? "On"
											: "Off"}
									</span>
								</div>
							</div>
						</div>
					</div>
					<div className="mt-auto flex justify-end gap-3">
						<Button
							variant="outline"
							onClick={handleClose}
							className="h-9"
						>
							Close
						</Button>
						<Button
							onClick={handleSave}
							className="h-9 bg-[#005893]"
						>
							Save
						</Button>
					</div>
				</div>
			</SheetContent>
		</Sheet>
	);
}

