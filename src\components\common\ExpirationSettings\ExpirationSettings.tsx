import type { ReactNode } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

export interface TimeInputProps {
	/** The current time value */
	value: string;
	/** Callback when the time value changes */
	onValueChange: (value: string) => void;
	/** The current time unit (days, hours, weeks, etc.) */
	unit: string;
	/** Callback when the time unit changes */
	onUnitChange: (unit: string) => void;
	/** Whether alternative option is enabled (e.g., specific date, never expires) */
	useAlternativeOption?: boolean;
	/** Callback when alternative option checkbox changes */
	onUseAlternativeOptionChange?: (useAlternative: boolean) => void;
	/** Label for the input section */
	label?: string;
	/** Custom width for the input container */
	containerWidth?: string;
	/** Available time units */
	units?: Array<{ value: string; label: string }>;
	/** Label for the alternative option checkbox */
	alternativeOptionLabel?: string;
	/** Custom placeholder for input */
	placeholder?: string;
	/** Whether the component is disabled */
	disabled?: boolean;
	/** Whether to show the alternative option checkbox */
	showAlternativeOption?: boolean;
	/** Custom class for the main container */
	className?: string;
	/** Layout orientation */
	orientation?: "horizontal" | "vertical";
	/** Custom label component */
	labelComponent?: ReactNode;
	/** Input type - number or text */
	inputType?: "number" | "text";
	/** Minimum value for number input */
	min?: number;
	/** Maximum value for number input */
	max?: number;
}

// Legacy interface for backward compatibility
export interface ExpirationSettingsProps {
	/** The current expiration value */
	value: string;
	/** Callback when the expiration value changes */
	onValueChange: (value: string) => void;
	/** The current time unit (days, hours, weeks) */
	unit: string;
	/** Callback when the time unit changes */
	onUnitChange: (unit: string) => void;
	/** Whether specific date is enabled */
	useSpecificDate: boolean;
	/** Callback when specific date checkbox changes */
	onUseSpecificDateChange?: (useSpecificDate: boolean) => void;
	/** Custom label for the section */
	label?: string;
	/** Custom width for the input container */
	containerWidth?: string;
	/** Available time units */
	units?: Array<{ value: string; label: string }>;
	/** Custom checkbox label */
	checkboxLabel?: string;
	/** Custom placeholder for input */
	placeholder?: string;
	/** Whether the component is disabled */
	disabled?: boolean;
	/** Layout orientation */
	orientation?: "horizontal" | "vertical";
	/** Whether to show the alternative option checkbox */
	showAlternativeOption?: boolean;
}

const DEFAULT_UNITS = [
	{ value: "minutes", label: "Minutes" },
	{ value: "hours", label: "Hours" },
	{ value: "days", label: "Days" },
	{ value: "weeks", label: "Weeks" },
	{ value: "months", label: "Months" },
];

export function TimeInput({
	value,
	onValueChange,
	unit,
	onUnitChange,
	useAlternativeOption = false,
	onUseAlternativeOptionChange,
	label,
	containerWidth = "w-[199px]",
	units = DEFAULT_UNITS,
	alternativeOptionLabel = "Use alternative option",
	placeholder,
	disabled = false,
	showAlternativeOption = true,
	className = "",
	orientation = "horizontal",
	labelComponent,
	inputType = "number",
	min,
	max,
}: TimeInputProps) {
	const isVertical = orientation === "vertical";

	return (
		<div className={`space-y-4 ${className}`}>
			<div
				className={`flex items-start ${isVertical ? "flex-col gap-3" : "justify-between"}`}
			>
				{(label || labelComponent) && (
					<div className={isVertical ? "" : "mt-1.5"}>
						{labelComponent || (
							<span className="text-base font-medium tracking-tight text-gray-800">
								{label}
							</span>
						)}
					</div>
				)}
				<div
					className={`flex flex-col items-start gap-3 ${containerWidth}`}
				>
					<div className="flex items-center gap-1 rounded-lg border border-zinc-200">
						<Input
							type={inputType}
							value={value}
							onChange={(e) => onValueChange(e.target.value)}
							placeholder={placeholder}
							disabled={disabled}
							min={min}
							max={max}
							className="h-9 border-none pr-0 text-xs shadow-none hover:border-none focus-visible:border-none focus-visible:ring-0 [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
						/>
						<Select
							value={unit}
							onValueChange={(value) =>
								onUnitChange(
									Array.isArray(value) ? value[0] : value
								)
							}
							disabled={disabled}
						>
							<SelectTrigger className="h-9 w-auto border-none text-xs shadow-none hover:border-none focus-visible:border-none">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								{units.map((unitOption) => (
									<SelectItem
										key={unitOption.value}
										value={unitOption.value}
									>
										{unitOption.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
					{showAlternativeOption && onUseAlternativeOptionChange && (
						<div className="flex items-center gap-2">
							<Checkbox
								id="alternative-option"
								checked={useAlternativeOption}
								onCheckedChange={(checked) =>
									onUseAlternativeOptionChange(
										checked === "indeterminate"
											? false
											: checked
									)
								}
								disabled={disabled}
							/>
							<label
								htmlFor="alternative-option"
								className="text-sm font-medium tracking-tight text-gray-800"
							>
								{alternativeOptionLabel}
							</label>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}

// Keep the old component for backward compatibility
export function ExpirationSettings({
	value,
	onValueChange,
	unit,
	onUnitChange,
	useSpecificDate,
	onUseSpecificDateChange,
	label = "Booking Link will Expire in",
	containerWidth = "w-[199px]",
	units = [
		{ value: "days", label: "Days" },
		{ value: "hours", label: "Hours" },
		{ value: "weeks", label: "Weeks" },
	],
	checkboxLabel = "Select Specific Date",
	placeholder,
	disabled = false,
	orientation = "horizontal",
	showAlternativeOption = true,
}: ExpirationSettingsProps) {
	return (
		<TimeInput
			value={value}
			onValueChange={onValueChange}
			unit={unit}
			onUnitChange={onUnitChange}
			useAlternativeOption={useSpecificDate}
			onUseAlternativeOptionChange={onUseSpecificDateChange}
			label={label}
			containerWidth={containerWidth}
			units={units}
			alternativeOptionLabel={checkboxLabel}
			placeholder={placeholder}
			disabled={disabled}
			orientation={orientation}
			showAlternativeOption={showAlternativeOption}
		/>
	);
}
