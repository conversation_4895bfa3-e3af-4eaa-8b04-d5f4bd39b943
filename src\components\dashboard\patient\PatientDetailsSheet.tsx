import React, { useState, useEffect } from "react";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	FileText,
	Send,
	Check,
	ChevronDown,
	Clock,
	Tag,
	Trash2,
	Edit,
	Store,
	CalendarClock,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { EditPatientSheet } from "./EditPatientSheet";
import {
	useClientDetail,
	transformClientDetailToPatientData,
} from "@/hooks/useClients";
import { RefreshCcw } from "lucide-react";

const visitHistoryData = [
	{
		id: 1,
		location: "Loaction Name",
		service: "Service name",
		doctor: "Dr. <PERSON>",
		date: "12 Aug 2024",
		time: "12:30am - 1:00am",
		status: "Upcoming",
	},
	{
		id: 2,
		location: "Loaction Name",
		service: "Service name",
		doctor: "Dr. <PERSON>",
		date: "13 Aug 2024",
		time: "2:00pm - 2:30pm",
		status: "No Show",
	},
	{
		id: 3,
		location: "Loaction Name",
		service: "Service name",
		doctor: "Dr. Michael Smith",
		date: "14 Aug 2024",
		time: "9:00am - 9:30am",
		status: "In Progress",
	},
	{
		id: 4,
		location: "Loaction Name",
		service: "Service name",
		doctor: "Dr. Sarah Lee",
		date: "15 Aug 2024",
		time: "10:00am - 10:30am",
		status: "Cancelled (Admin)",
	},
	{
		id: 5,
		location: "Loaction Name",
		service: "Service name",
		doctor: "Dr. John Doe",
		date: "16 Aug 2024",
		time: "11:00am - 11:30am",
		status: "Completed",
	},
];

const getStatusBadgeStyle = (status: string) => {
	switch (status) {
		case "Upcoming":
			return "bg-cyan-100 text-cyan-900";
		case "No Show":
			return "bg-red-100 text-red-600";
		case "In Progress":
			return "bg-amber-100 text-amber-700";
		case "Cancelled (Admin)":
			return "bg-cyan-900/10 text-cyan-900";
		case "Completed":
			return "bg-green-100 text-green-700";
		default:
			return "bg-gray-100 text-gray-700";
	}
};

interface PatientDetailsSheetProps {
	open: boolean;
	onClose: () => void;
	clientId?: string | number;
	patient?: {
		name: string;
		status: string;
	};
}

export const PatientDetailsSheet: React.FC<PatientDetailsSheetProps> = ({
	open,
	onClose,
	clientId,
	patient = { name: "Unknown Patient", status: "Unknown" },
}) => {
	const [activeTab, setActiveTab] = useState("details");
	const [newMessage, setNewMessage] = useState("");
	const [isActive, setIsActive] = useState(false);
	const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);

	const {
		data: clientDetailData,
		isLoading,
		error,
		refetch,
	} = useClientDetail(clientId || "", {
		enabled: !!clientId && open,
	});

	useEffect(() => {
		if (clientDetailData?.data?.is_active !== undefined) {
			setIsActive(clientDetailData.data.is_active);
		}
	}, [clientDetailData]);

	const handleEdit = () => {
		setIsEditSheetOpen(true);
	};

	const handleEditSheetClose = () => {
		setIsEditSheetOpen(false);
	};

	const handleEditSubmit = (data: any) => {
		console.log("Updated patient data:", data);
		setIsEditSheetOpen(false);
	};

	const handleSendMessage = () => {
		if (newMessage.trim()) {
			console.log("Sending message:", newMessage);
			setNewMessage("");
		}
	};

	const patientData = clientDetailData
		? transformClientDetailToPatientData(clientDetailData)
		: {
				name: patient.name,
				priority: "High",
				status: patient.status,
				email: "<EMAIL>",
				phone: "****** 567 890",
				patientId: "21789057",
				categories: ["Mental Health", "Black Student", "LGBTQ"],
				customIntake: [
					{ label: "Custom Intake 1", value: "12 Aug 2024" },
					{ label: "Custom Intake 2", value: "123 Main Street" },
					{ label: "Custom Intake 3", value: "Canada" },
				],
				forms: [
					{
						name: "[Form Name]",
						type: "Health Check Up",
						doctor: "Dr. Abraham Johnson",
						date: "12 Aug 2024",
					},
				],
				messages: [
					{
						id: 1,
						text: "Hello!",
						sender: "patient",
						time: "12:55 am",
					},
				],
			};

	const getInitials = (name: string) => {
		return name
			.split(" ")
			.map((n) => n[0])
			.join("")
			.toUpperCase();
	};

	const tabItems = [
		{ value: "details", label: "Details" },
		{ value: "forms", label: "Forms" },
		{ value: "message", label: "Message" },
		{ value: "visit-history", label: "Visit History" },
	];

	if (isLoading && clientId) {
		return (
			<Sheet open={open} onOpenChange={onClose}>
				<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
					<div className="flex min-h-[400px] items-center justify-center">
						<div className="text-center">
							<RefreshCcw className="mx-auto h-8 w-8 animate-spin text-gray-400" />
							<p className="mt-2 text-sm text-gray-500">
								Loading client details...
							</p>
						</div>
					</div>
				</SheetContent>
			</Sheet>
		);
	}

	if (error && clientId) {
		return (
			<Sheet open={open} onOpenChange={onClose}>
				<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
					<div className="flex min-h-[400px] items-center justify-center">
						<div className="text-center">
							<p className="text-sm text-red-600">
								Failed to load client details
							</p>
							<Button
								variant="outline"
								onClick={() => refetch()}
								className="mt-2"
							>
								Try Again
							</Button>
						</div>
					</div>
				</SheetContent>
			</Sheet>
		);
	}
	return (
		<Sheet open={open} onOpenChange={onClose}>
			<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
				<div className="flex flex-col items-start justify-start gap-4">
					<div className="inline-flex w-full items-center justify-start gap-4">
						<div className="flex flex-1 items-center justify-start gap-2">
							<Avatar className="h-7 w-7 rounded-full">
								{clientDetailData?.data
									?.profile_picture_url && (
									<AvatarImage
										src={
											clientDetailData.data
												.profile_picture_url
										}
										alt={patientData.name}
									/>
								)}
								<AvatarFallback className="text-base font-semibold">
									{getInitials(patientData.name)}
								</AvatarFallback>
							</Avatar>
							<div className="text-lg font-semibold">
								{patientData.name}
							</div>
							<div className="flex items-center gap-1 rounded-md bg-[#FEE2E2] px-2 py-1 text-[#DC2626]">
								<Tag className="h-3 w-3" />
								<span className="text-[10px] font-medium">
									High
								</span>
							</div>
						</div>
						<div className="flex items-center justify-start gap-2">
							<span className="text-xs font-medium">Active</span>
							<div className="flex items-center gap-1.5">
								<Switch
									checked={isActive}
									onCheckedChange={setIsActive}
								/>
								<span className="text-xs text-gray-500">
									{isActive ? "On" : "Off"}
								</span>
							</div>
						</div>
					</div>
					<div className="flex w-full flex-col">
						<div className="border-t border-gray-200 py-3">
							<div className="text-[10px] text-gray-500">
								Email Address
							</div>
							<div className="text-sm">{patientData.email}</div>
						</div>
						<div className="flex w-full">
							<div className="flex-1 border-t border-gray-200 py-3">
								<div className="text-[10px] text-gray-500">
									Phone Number
								</div>
								<div className="text-sm">
									{patientData.phone}
								</div>
							</div>
							<div className="flex-1 border-t border-gray-200 py-3">
								<div className="text-[10px] text-gray-500">
									Patient ID
								</div>
								<div className="text-sm">
									{patientData.patientId}
								</div>
							</div>
						</div>
						<div className="border-t border-gray-200 py-3">
							<div className="text-[10px] text-gray-500">
								Category
							</div>
							<div className="overflow-x-scroll [&::-webkit-scrollbar]:hidden">
								<div className="flex gap-[5px]">
									{patientData.categories.map(
										(category: string, index: number) => (
											<div
												key={index}
												className={`h-[22px rounded-md px-2 py-1 text-nowrap ${
													category === "Mental Health"
														? "bg-blue-100"
														: category ===
															  "Black Student"
															? "bg-gray-200"
															: "bg-amber-100"
												}`}
											>
												<div className="text-[10px] font-medium">
													{category}
												</div>
											</div>
										)
									)}
								</div>
							</div>
						</div>
					</div>
					<div className="flex h-10 w-full rounded-lg bg-gray-100 p-1">
						{tabItems.map((tab) => (
							<div
								key={tab.value}
								onClick={() => setActiveTab(tab.value)}
								className={`flex h-8 flex-1 cursor-pointer items-center justify-center px-3 py-1 ${
									tab.value === activeTab
										? "rounded-md bg-white shadow-sm"
										: ""
								}`}
							>
								<div
									className={`text-xs font-medium ${
										tab.value === activeTab
											? "text-gray-900"
											: "text-gray-500"
									}`}
								>
									{tab.label}
								</div>
							</div>
						))}
					</div>
					{activeTab === "details" && (
						<div className="flex w-full flex-col">
							{patientData.customIntake.map(
								(item: any, index: number) => (
									<div
										key={index}
										className="border-t border-gray-200 py-3"
									>
										<div className="text-[10px] text-gray-500">
											{item.label}
										</div>
										<div className="text-sm">
											{item.value}
										</div>
									</div>
								)
							)}
						</div>
					)}

					{activeTab === "forms" && (
						<div className="flex w-full flex-col">
							{patientData.forms.map((form, index) => (
								<div
									key={index}
									className="border-t border-gray-200 py-3"
								>
									<div className="flex items-center justify-between">
										<div className="text-sm font-medium">
											{form.name}
										</div>
										<div className="flex gap-2">
											<Button
												variant="outline"
												size="icon"
												className="h-8 w-8 p-2"
											>
												<FileText className="h-3 w-3" />
											</Button>
											<Button
												variant="outline"
												size="icon"
												className="h-8 w-8 p-2"
											>
												<Trash2 className="h-3 w-3" />
											</Button>
										</div>
									</div>
									<div className="mt-2 flex items-center gap-4">
										<div className="rounded-md bg-gray-100 px-2 py-1">
											<span className="text-[10px] font-medium">
												{form.type}
											</span>
										</div>
										<div className="flex items-center gap-2 text-[#71717A]">
											<Store className="h-2.5 w-2.5" />
											<span className="text-[10px]">
												{form.doctor}
											</span>
										</div>
										<div className="flex items-center gap-2">
											<CalendarClock className="h-2.5 w-2.5" />
											<span className="text-[10px]">
												{form.date}
											</span>
										</div>
									</div>
								</div>
							))}
						</div>
					)}

					{activeTab === "message" && (
						<div className="flex w-full flex-col border-t border-gray-200">
							<div className="flex h-[300px] flex-col items-start justify-end gap-3 rounded-lg bg-gray-50 p-2">
								<div className="flex items-center justify-center gap-4 self-stretch">
									<div className="h-0 flex-1 border-t border-gray-200"></div>
									<div className="text-center text-xs font-medium text-gray-400">
										Today
									</div>
									<div className="h-0 flex-1 border-t border-gray-200"></div>
								</div>
								{patientData.messages.map((message) =>
									message.sender === "patient" ? (
										<div
											key={message.id}
											className="flex items-start justify-start gap-2 self-stretch"
										>
											<div className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-200">
												<div className="text-center text-xs font-medium text-gray-400">
													AB
												</div>
											</div>
											<div className="flex max-w-[288px] flex-col items-start justify-start gap-1">
												<div className="rounded-md bg-[#005893]/25 px-2 py-1.5">
													<div className="text-xs text-gray-900">
														{message.text}
													</div>
												</div>
												<div className="flex items-center justify-start gap-1">
													<div className="text-[8px] text-gray-900">
														{message.time}
													</div>
												</div>
											</div>
										</div>
									) : (
										<div
											key={message.id}
											className="flex items-start justify-end gap-2 self-stretch"
										>
											<div className="flex max-w-[288px] flex-col items-end justify-start gap-1">
												<div className="rounded-md bg-[#E4E4E7] px-2 py-1.5">
													<div className="text-xs text-gray-900">
														{message.text}
													</div>
												</div>
												<div className="flex items-center justify-end gap-1">
													<Check className="h-3.5 w-3.5 text-blue-500" />
													<div className="text-[8px] text-gray-900">
														{message.time}
													</div>
												</div>
											</div>
											<div className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-200">
												<div className="text-center text-xs font-medium text-gray-400">
													AB
												</div>
											</div>
										</div>
									)
								)}
								<div className="mt-auto flex items-center gap-2 self-stretch py-2">
									<Input
										className="h-9 flex-1 text-xs"
										placeholder="Enter message"
										value={newMessage}
										onChange={(e) =>
											setNewMessage(e.target.value)
										}
										onKeyDown={(e) => {
											if (e.key === "Enter")
												handleSendMessage();
										}}
									/>
									<Button
										className="h-9 w-9 bg-[#005893] p-0"
										onClick={handleSendMessage}
									>
										<Send className="h-3 w-3 text-white" />
									</Button>
								</div>
							</div>
						</div>
					)}

					{activeTab === "visit-history" && (
						<div className="flex w-full flex-col border-t border-gray-200">
							<div className="overflow-hidden rounded-xl border border-gray-200">
								{visitHistoryData.map((visit, index) => (
									<div
										key={visit.id}
										className={`flex items-center justify-between border-t border-gray-200 ${index === 0 ? "border-t-0" : ""}`}
									>
										<div className="flex min-w-[80px] flex-1 flex-col gap-1 p-3">
											<div className="text-xs">
												{visit.location}
											</div>
											<div className="flex flex-col gap-0.5">
												<div className="text-[10px] font-medium">
													{visit.service}
												</div>
												<div className="text-[10px] text-gray-500">
													{visit.doctor}
												</div>
											</div>
										</div>
										<div className="flex w-36 min-w-[80px] gap-2 p-3">
											<div className="p-0.5">
												<Clock className="h-3 w-3 text-gray-500" />
											</div>
											<div className="flex flex-col gap-0.5">
												<div className="text-xs">
													{visit.date}
												</div>
												<div className="text-[10px] text-gray-500">
													{visit.time}
												</div>
											</div>
										</div>
										<div className="w-40 min-w-[80px] p-3">
											<div className="flex h-5 items-center justify-end gap-px">
												<div
													className={`rounded-md px-2 py-1 ${getStatusBadgeStyle(visit.status)}`}
												>
													<div className="text-[10px] font-medium">
														{visit.status}
													</div>
												</div>
												<Button
													variant="ghost"
													size="sm"
													className="h-6 w-6 p-0"
												>
													<ChevronDown className="h-3 w-3" />
												</Button>
											</div>
										</div>
									</div>
								))}
							</div>
						</div>
					)}
				</div>
				<div className="mt-auto flex w-full items-center justify-between gap-2.5 pt-7">
					<Button
						variant="outline"
						size="icon"
						className="h-9 w-9 border-[#DC2626]"
					>
						<Trash2 className="h-3.5 w-3.5 text-[#DC2626]" />
					</Button>
					<div className="flex flex-1 items-center justify-end gap-3">
						<Button
							variant="outline"
							onClick={onClose}
							className="h-9"
						>
							Close
						</Button>
						<Button onClick={handleEdit} className="h-9">
							<Edit />
							Edit
						</Button>
					</div>
				</div>
			</SheetContent>
			<EditPatientSheet
				open={isEditSheetOpen}
				onOpenChange={handleEditSheetClose}
				onSubmit={handleEditSubmit}
				clientId={clientId?.toString()}
			/>
		</Sheet>
	);
};
