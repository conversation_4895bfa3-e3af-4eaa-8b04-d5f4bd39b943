import { createBrowserRouter } from "react-router";
import { MainLayout } from "../layouts/MainLayout";
import Dashboard from "../pages/Dashboard/Dashboard";
import DashboardTest from "../pages/Dashboard/DashboardTest";
import { Locations } from "@/pages/WorkPlace";
import {
	SignInPage,
	ForgotPasswordPage,
	ResetPasswordPage,
	MFAPage,
} from "../pages/Auth";
import { ManagementTabs } from "@/pages/WorkPlace/ManagementTabs";
import { Clients } from "@/pages/ManageClients";
import ImportCSV from "@/pages/ManageClients/ImportCSV";
import PatientCategories from "@/pages/ManageClients/PatientCategories";
import PatientReviews from "@/pages/ManageClients/PatientReviews";
import SchedulePage from "@/pages/schedules";
import LocationProviders from "@/pages/WorkPlace/LocationProviders";
import LocationProviderDetails from "@/pages/WorkPlace/LocationProviderDetails";
import { AllFormsPage } from "@/pages/Forms";
import { CreateFormPage } from "@/pages/Forms/CreateFormPage";

export const router = createBrowserRouter([
	{
		path: "/",
		Component: MainLayout,
		children: [
			// Home/landing page
			{
				index: true,
				Component: DashboardTest,
			},
			// Dashboard section
			{
				path: "dashboard",
				// Component: MainLayout, // Optional dashboard-specific layout
				children: [
					{
						index: true,
						Component: Dashboard,
					},
					{
						path: "test",
						Component: DashboardTest,
					},
					{
						path: "workplace",
						// Component: Workplace,
						children: [
							{
								index: true,
								// Component: ManagementTabs, // Main workplace page with tabs
								Component: Locations, // Main workplace page with tabs
							},
							{
								path: "locations",
								Component: Locations,
							},
							{
								path: "providers/:providerId",
								Component: LocationProviders,
							},
							{
								path: "providers/provider-details/:providerId",
								Component: LocationProviderDetails,
							},
						],
					},
					{
						path: "patients",
						children: [
							{
								index: true,
								Component: Clients,
							},
							{
								path: "import-csv",
								Component: ImportCSV,
							},
							{
								path: "categories",
								Component: PatientCategories,
							},
							{
								path: "reviews",
								Component: PatientReviews,
							},
						],
					},

					// Future dashboard routes
					// {
					//   path: "analytics",
					//   Component: Analytics,
					//   loader: async ({ request }) => {
					//     const user = await getCurrentUser();
					//     if (!user.permissions.includes('analytics.read')) {
					//       throw new Response("Unauthorized", { status: 401 });
					//     }
					//     return { analytics: await getAnalytics() };
					//   },
					// },
				],
			},
		],
	},
	{
		path: "sign-in",
		Component: SignInPage,
	},
	{
		path: "forgot-password",
		Component: ForgotPasswordPage,
	},
	{
		path: "reset-password",
		Component: ResetPasswordPage,
	},
	{
		path: "2fa",
		Component: MFAPage,
	},
]);

// export const router = createBrowserRouter([
// 	{
// 		path: "/",
// 		Component: MainLayout,
// 		children: [
// 			// Index route - renders at "/"
// 			{
// 				index: true,
// 				Component: Dashboard,
// 			},
// 			// Dashboard routes
// 			{
// 				path: "dashboard",
// 				Component: Dashboard,
// 			},
// 			{
// 				path: "dashboard/test",
// 				Component: DashboardTest,
// 			},
// 			// Future routes
// 			// {
// 			//   path: "customers",
// 			//   Component: Customers,
// 			//   loader: async ({ request }) => {
// 			//     const user = await getCurrentUser();
// 			//     if (!user.permissions.includes('customers.read')) {
// 			//       throw new Response("Unauthorized", { status: 401 });
// 			//     }
// 			//     return { customers: await getCustomers() };
// 			//   },
// 			// },
// 		],
// 	},
// ]);
