import { But<PERSON> } from "@/components/ui/button";
import { LuCalendarPlus } from "react-icons/lu";
import { IoSettingsOutline } from "react-icons/io5";
// import CalendarView from "./calendar-view";
import Filter from "./filter";
import Calendar from "./calendar";
import FilterAppointment from "./filter-appointment";
import BookAppointment from "./book-appointment";
import { useState } from "react";

export default function SchedulePage() {
    const [appointmentOpen, setAppointmentOpen] = useState<boolean>(false);
    return (
        <section>
            {/* header */}

            <div className="flex items-center justify-between py-3 px-5">
                <div>
                    <h1 className="text-[#18181B] font-bold text-4xl mb-2.5">Manage Schedule</h1>
                    <p className="text-[#71717A] font-regular text-base">[Location Name]</p>
                </div>
                <div className="flex items-center gap-x-2.5">
                    <FilterAppointment />
                    <Button variant="outline" className="py-5">
                        <IoSettingsOutline />
                    </Button>
                    <Button
                        className="py-5 cursor-pointer"
                        type="button"
                        onClick={() => setAppointmentOpen(true)}
                    >
                        <LuCalendarPlus />
                        Add Appointment
                    </Button>
                </div>
            </div>

            <BookAppointment
                open={appointmentOpen}
                onOpenChange={setAppointmentOpen}
            />


            <div className="grid w-full max-w-screen grid-cols-[400px_1fr] gap-x-7 mt-4 h-screen max-h-screen">

                <Filter />

                <div className="overflow-x-auto w-full">
                    <Calendar />
                </div>

            </div>

        </section>
    );
}