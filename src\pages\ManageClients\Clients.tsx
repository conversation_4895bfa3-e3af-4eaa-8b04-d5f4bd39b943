import { useUIStore } from "@/stores/uiStore";
import { useEffect, useCallback, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Settings, Plus, Settings2, RefreshCcw, Upload } from "lucide-react";
import { EmptyContent } from "@/components/ui-components/EmptyContent";
import {
	AllPatientListCard,
	type Patient,
} from "@/components/ui-components/AllPatientListCard";
import { Checkbox } from "@/components/common/Checkbox";
import {
	Pagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";
import { PatientDetailsSheet } from "@/components/dashboard/patient/PatientDetailsSheet";
import {
	AddPatientSheet,
	type PatientFormData,
} from "@/components/dashboard/patient/AddPatientSheet";
import { PatientSettingsSheet } from "@/components/dashboard/patient/PatientSettingsSheet";
import type { PatientSettings } from "@/components/dashboard/patient/PatientSettingsSheet";
import {
	useClients,
	transformClientToPatient,
	useDeleteClient,
} from "@/hooks/useClients";
import { useModal } from "@/lib/hooks/useModal";

export default function Clients() {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setPageHeaderContent = useUIStore(
		(state) => (state as any).setPageHeaderContent
	);
	const { openModal } = useModal();

	const [currentPage, setCurrentPage] = useState(1);
	const patientsPerPage = 7;

	const {
		data: clientsData,
		isLoading,
		error,
		refetch,
	} = useClients({
		page: currentPage,
		per_page: patientsPerPage,
	});

	const deleteClientMutation = useDeleteClient({
		onSuccess: () => {
			refetch();
		},
	});

	const patients: Patient[] =
		(clientsData as any)?.data?.map(transformClientToPatient) || [];
	const totalPages = (clientsData as any)?.meta?.total_pages || 1;
	const hasPatients = patients.length > 0;

	const [selectedPatients, setSelectedPatients] = useState<string[]>([]);
	const [selectedPatientForDetails, setSelectedPatientForDetails] =
		useState<Patient | null>(null);
	const [isPatientDetailsOpen, setIsPatientDetailsOpen] = useState(false);
	const [isAddPatientOpen, setIsAddPatientOpen] = useState(false);
	const [isPatientSettingsOpen, setIsPatientSettingsOpen] = useState(false);

	const currentPatients = patients;

	const handleSearch = useCallback(() => {
		console.log("Search clicked");
	}, []);

	const handleSettings = useCallback(() => {
		setIsPatientSettingsOpen(true);
	}, []);

	const handleImportCSV = useCallback(() => {
		window.location.href = "/dashboard/patients/import-csv";
	}, []);

	const handleRefresh = useCallback(() => {
		refetch();
	}, [refetch]);

	const handleAddPatient = useCallback(() => {
		setIsAddPatientOpen(true);
	}, []);

	const handleAddPatientSubmit = useCallback(
		(data: PatientFormData) => {
			console.log("New patient data:", data);
			refetch();
			setSelectedPatients([]);
			setCurrentPage(1);
		},
		[refetch]
	);

	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
		setSelectedPatients([]);
		console.log("Changed to page:", page);
	}, []);

	const handlePreviousPage = useCallback(() => {
		if (currentPage > 1) {
			handlePageChange(currentPage - 1);
		}
	}, [currentPage, handlePageChange]);

	const handleNextPage = useCallback(() => {
		if (currentPage < totalPages) {
			handlePageChange(currentPage + 1);
		}
	}, [currentPage, totalPages, handlePageChange]);

	const handleEditPatient = useCallback((patient: Patient) => {
		console.log("Edit patient:", patient.id);
	}, []);

	const handleOpenPatientDetails = useCallback((patient: Patient) => {
		setSelectedPatientForDetails(patient);
		setIsPatientDetailsOpen(true);
		console.log("Opening patient details for:", patient.name);
	}, []);

	const handleClosePatientDetails = useCallback(() => {
		setIsPatientDetailsOpen(false);
		setSelectedPatientForDetails(null);
		console.log("Closed patient details");
	}, []);

	const handleDeletePatient = useCallback(
		(patient: Patient) => {
			openModal("confirmation", {
				size: "md",
				data: {
					title: "Delete Patient",
					message: `Are you sure you want to delete ${patient.name}? This action cannot be undone.`,
					confirmText: "Delete",
					cancelText: "Cancel",
					variant: "destructive",
					onConfirm: () => {
						deleteClientMutation.mutate(patient.id);
					},
				},
			});
		},
		[openModal, deleteClientMutation]
	);

	const handleInfoPatient = useCallback((patient: Patient) => {
		console.log("Call patient:", patient.phone);
	}, []);

	const handleEmailPatient = useCallback((patient: Patient) => {
		console.log("Email patient:", patient.email);
	}, []);

	const handleSavePatientSettings = useCallback(
		(settings: PatientSettings) => {
			console.log("Saving patient settings:", settings);
		},
		[]
	);

	const handleSelectAll = useCallback(
		(checked: boolean) => {
			if (checked) {
				const currentPagePatientIds = currentPatients.map(
					(patient) => patient.id
				);
				setSelectedPatients((prev) => {
					const newSelections = [
						...prev,
						...currentPagePatientIds.filter(
							(id) => !prev.includes(id)
						),
					];
					console.log(
						"Selected all patients on current page:",
						currentPagePatientIds
					);
					return newSelections;
				});
			} else {
				const currentPagePatientIds = currentPatients.map(
					(patient) => patient.id
				);
				setSelectedPatients((prev) => {
					const newSelections = prev.filter(
						(id) => !currentPagePatientIds.includes(id)
					);
					console.log(
						"Deselected all patients on current page:",
						currentPagePatientIds
					);
					return newSelections;
				});
			}
		},
		[currentPatients]
	);

	const handlePatientSelection = useCallback(
		(patientId: string, selected: boolean) => {
			if (selected) {
				setSelectedPatients((prev) => {
					const newSelection = [...prev, patientId];
					console.log(
						"Selected patient:",
						patientId,
						"Total selected:",
						newSelection.length
					);
					return newSelection;
				});
			} else {
				setSelectedPatients((prev) => {
					const newSelection = prev.filter((id) => id !== patientId);
					console.log(
						"Deselected patient:",
						patientId,
						"Total selected:",
						newSelection.length
					);
					return newSelection;
				});
			}
		},
		[]
	);

	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Patients",
				href: "/dashboard/patients",
			},
			{
				label: "All Patients",
				isCurrentPage: true,
			},
		]);
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	useEffect(() => {
		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				<h1 className="text-foreground text-2xl font-bold">
					List of Patients
				</h1>
				<div className="flex items-center space-x-3">
					<Button variant="outline" size="icon" className="h-9 w-9">
						<Settings2 className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						onClick={handleSettings}
						className="h-9 w-9"
					>
						<Settings className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						onClick={handleRefresh}
						disabled={isLoading}
						className="h-9 w-9"
					>
						<RefreshCcw
							className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
						/>
					</Button>

					<Button
						variant="outline"
						onClick={handleImportCSV}
						className="h-9 px-4"
					>
						<Upload className="h-4 w-4" />
						Import CSV
					</Button>
					<Button
						variant="default"
						onClick={handleAddPatient}
						className="h-9 bg-[#005893] px-4 text-white hover:bg-[#004a7a]"
					>
						<Plus className="h-4 w-4" />
						Add Patient
					</Button>
				</div>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setPageHeaderContent(null);
		};
	}, [
		setPageHeaderContent,
		handleSearch,
		handleSettings,
		handleImportCSV,
		handleAddPatient,
		handleRefresh,
		isLoading,
	]);

	if (isLoading && currentPage === 1) {
		return (
			<div className="flex min-h-[400px] items-center justify-center">
				<div className="text-center">
					<RefreshCcw className="mx-auto h-8 w-8 animate-spin text-gray-400" />
					<p className="mt-2 text-sm text-gray-500">
						Loading clients...
					</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex min-h-[400px] items-center justify-center">
				<div className="text-center">
					<p className="text-sm text-red-600">
						Failed to load clients
					</p>
					<Button
						variant="outline"
						onClick={handleRefresh}
						className="mt-2"
					>
						Try Again
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-">
			{!hasPatients ? (
				<div className="flex min-h-[400px] items-center justify-center">
					<EmptyContent
						title="No patients added"
						description="Select file or Drag and drop here to import list CSV files up to 50 MB are accepted, or via Google Sheets and Microsoft Excel."
						actions={[
							{
								label: "Add New",
								onClick: handleAddPatient,
								variant: "primary",
							},
							{
								label: "Import",
								onClick: handleImportCSV,
								variant: "outline",
							},
						]}
					/>
				</div>
			) : (
				<div>
					<div className="mt-2 rounded-xl border border-[#E4E4E7]">
						<div className="flex items-center border-b border-gray-200 py-[15.5px]">
							<div className="flex items-center px-4">
								<Checkbox
									checked={
										currentPatients.length > 0 &&
										currentPatients.every((patient) =>
											selectedPatients.includes(
												patient.id
											)
										)
									}
									onCheckedChange={handleSelectAll}
									className="border-[#005893]"
								/>
							</div>

							<div className="flex w-72 min-w-20 items-center gap-3 px-3">
								<div className="flex items-center gap-2">
									<p className="text-main-1 text-sm text-[14px] font-bold">
										Name
									</p>
									{selectedPatients.length > 0 && (
										<span className="text-xs text-gray-500">
											({selectedPatients.length} selected
											total)
										</span>
									)}
								</div>
							</div>

							<div className="flex w-32 min-w-20 items-center px-3">
								<p className="text-main-1 text-sm text-[14px] font-bold">
									Status
								</p>
							</div>

							<div className="flex w-60 min-w-20 items-center px-3">
								<p className="text-main-1 text-sm text-[14px] font-bold">
									Email
								</p>
							</div>

							<div className="flex w-40 min-w-20 items-center px-3">
								<p className="text-main-1 text-sm text-[14px] font-bold">
									Phone
								</p>
							</div>
							<div className="flex w-36 min-w-20 items-center px-3">
								<p className="text-main-1 text-sm text-[14px] font-bold">
									Last Visit
								</p>
							</div>
						</div>

						{currentPatients.map((patient) => (
							<AllPatientListCard
								key={patient.id}
								patient={patient}
								checked={selectedPatients.includes(patient.id)}
								onCheckboxChange={handlePatientSelection}
								onClick={() =>
									handleOpenPatientDetails(patient)
								}
								className="cursor-pointer hover:bg-gray-50"
								actions={[
									{
										type: "delete",
										onClick: handleDeletePatient,
									},
									{
										type: "edit",
										onClick: handleEditPatient,
									},
									{
										type: "email",
										onClick: handleEmailPatient,
									},
									{
										type: "info",
										onClick: handleInfoPatient,
									},
								]}
							/>
						))}
					</div>
				</div>
			)}
			{totalPages > 1 && (
				<div className="mt-2 flex justify-end">
					<div>
						<Pagination>
							<PaginationContent>
								<PaginationItem>
									<PaginationPrevious
										onClick={handlePreviousPage}
										className={
											currentPage === 1
												? "pointer-events-none opacity-50"
												: "cursor-pointer"
										}
									/>
								</PaginationItem>
								{Array.from(
									{ length: totalPages },
									(_, i) => i + 1
								).map((page) => (
									<PaginationItem key={page}>
										<PaginationLink
											onClick={() =>
												handlePageChange(page)
											}
											isActive={currentPage === page}
											className="cursor-pointer"
										>
											{page}
										</PaginationLink>
									</PaginationItem>
								))}

								{totalPages > 5 &&
									currentPage < totalPages - 2 && (
										<PaginationItem>
											<PaginationEllipsis />
										</PaginationItem>
									)}

								<PaginationItem>
									<PaginationNext
										onClick={handleNextPage}
										className={
											currentPage === totalPages
												? "pointer-events-none opacity-50"
												: "cursor-pointer"
										}
									/>
								</PaginationItem>
							</PaginationContent>
						</Pagination>
					</div>
				</div>
			)}

			<PatientDetailsSheet
				open={isPatientDetailsOpen}
				onClose={handleClosePatientDetails}
				clientId={selectedPatientForDetails?.id}
				patient={
					selectedPatientForDetails
						? {
								name: selectedPatientForDetails.name,
								status: selectedPatientForDetails.status,
							}
						: undefined
				}
			/>
			<AddPatientSheet
				open={isAddPatientOpen}
				onOpenChange={setIsAddPatientOpen}
				onSubmit={handleAddPatientSubmit}
			/>
			<PatientSettingsSheet
				open={isPatientSettingsOpen}
				onOpenChange={setIsPatientSettingsOpen}
				onSave={handleSavePatientSettings}
			/>
		</div>
	);
}
