import React, { useState, useEffect } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useBusinessAttributesForForm } from "@/hooks/useBusinessAttributes";
import { useClientDetail, useUpdateClient } from "@/hooks/useClients";
import type { BusinessAttribute } from "@/types/businessAttributes";

interface EditPatientSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: EditPatientFormData) => void;
	clientId?: string;
}

export interface EditPatientFormData {
	firstName: string;
	lastName: string;
	email: string;
	phone: string;
	isActive: boolean;
	customFields?: Record<string, string | number | boolean>;
}

export function EditPatientSheet({
	open,
	onOpenChange,
	onSubmit,
	clientId,
}: EditPatientSheetProps) {
	const { data: businessAttributesData, isLoading: isLoadingAttributes } =
		useBusinessAttributesForForm();
	const businessAttributes: BusinessAttribute[] =
		(businessAttributesData as any)?.data || [];

	const {
		data: clientDetailData,
		isLoading: isLoadingClient,
		error,
		refetch,
	} = useClientDetail(clientId || "", {
		enabled: !!clientId && open,
	});

	const updateClientMutation = useUpdateClient({
		onSuccess: () => {
			onSubmit?.(formData);
			onOpenChange(false); 
		},
		onError: (error) => {
			console.error("Error updating client:", error);
		},
	});

	const [formData, setFormData] = useState<EditPatientFormData>({
		firstName: "",
		lastName: "",
		email: "",
		phone: "",
		isActive: false,
		customFields: {},
	});

	useEffect(() => {
		if (businessAttributes.length > 0) {
			const initialCustomFields: Record<
				string,
				string | number | boolean
			> = {};
			businessAttributes.forEach((attr) => {
				switch (attr.type) {
					case "checkbox":
						initialCustomFields[attr.key] = false;
						break;
					case "number":
						initialCustomFields[attr.key] = "";
						break;
					default:
						initialCustomFields[attr.key] = "";
				}
			});
			setFormData((prev) => ({
				...prev,
				customFields: initialCustomFields,
			}));
		}
	}, [businessAttributes]);

	useEffect(() => {
		if (open && clientDetailData?.data) {
			const clientData = clientDetailData.data;
			const attributesMap: Record<string, any> = {};
			if (clientData.attributes && Array.isArray(clientData.attributes)) {
				clientData.attributes.forEach((attr) => {
					attributesMap[attr.key] = attr.value;
				});
			}
			const initialCustomFields: Record<
				string,
				string | number | boolean
			> = {};
			businessAttributes.forEach((attr) => {
				switch (attr.type) {
					case "checkbox":
						initialCustomFields[attr.key] =
							attributesMap[attr.key] ?? false;
						break;
					case "number":
						initialCustomFields[attr.key] =
							attributesMap[attr.key] ?? "";
						break;
					default:
						initialCustomFields[attr.key] =
							attributesMap[attr.key] ?? "";
				}
			});

			setFormData({
				firstName: clientData.first_name || "",
				lastName: clientData.last_name || "",
				email: clientData.email || "",
				phone: clientData.phone_number || "",
				isActive: clientData.is_active ?? true,
				customFields: initialCustomFields,
			});
		}
	}, [open, clientDetailData, businessAttributes]);

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value, type, checked } = e.target;

		if (name.startsWith("customFields.")) {
			const fieldName = name.split(".")[1];
			let fieldValue: string | number | boolean = value;
			if (type === "checkbox") {
				fieldValue = checked;
			} else if (type === "number") {
				fieldValue = value === "" ? "" : Number(value);
			}

			setFormData({
				...formData,
				customFields: {
					...formData.customFields,
					[fieldName]: fieldValue,
				},
			});
		} else {
			setFormData({
				...formData,
				[name]: value,
			});
		}
	};

	const handleActiveToggle = (checked: boolean) => {
		setFormData({
			...formData,
			isActive: checked,
		});
	};

	const resetForm = () => {
		const resetCustomFields: Record<string, string | number | boolean> = {};
		businessAttributes.forEach((attr) => {
			switch (attr.type) {
				case "checkbox":
					resetCustomFields[attr.key] = false;
					break;
				case "number":
					resetCustomFields[attr.key] = "";
					break;
				default:
					resetCustomFields[attr.key] = "";
			}
		});

		setFormData({
			firstName: "",
			lastName: "",
			email: "",
			phone: "",
			isActive: false,
			customFields: resetCustomFields,
		});
	};

	const handleSubmit = async () => {
		if (
			!formData.firstName ||
			!formData.lastName ||
			!formData.email ||
			!formData.phone ||
			!clientId
		) {
			return;
		}

		const updateData = {
			first_name: formData.firstName,
			last_name: formData.lastName,
			email: formData.email,
			phone_number: formData.phone,
			is_active: formData.isActive,
			attributes: formData.customFields || {},
		};

		updateClientMutation.mutate({
			id: clientId,
			data: updateData,
		});
	};

	const handleClose = () => {
		resetForm();
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={handleClose}>
			<SheetContent className="w-full !max-w-[525px] overflow-y-auto p-6 [&>button]:hidden">
				<div className="flex h-full flex-col gap-6">
					<SheetHeader className="flex-row items-center justify-between space-y-0 px-0">
						<div className="flex flex-1 items-center justify-start gap-2">
							<div className="text-lg font-semibold">
								Edit Details
							</div>
						</div>
						<div className="flex items-center justify-start gap-2">
							<span className="text-xs font-medium">Active</span>
							<div className="flex items-center gap-1.5">
								<Switch
									checked={formData.isActive}
									onCheckedChange={handleActiveToggle}
								/>
								<span className="text-xs text-gray-500">
									{formData.isActive ? "On" : "Off"}
								</span>
							</div>
						</div>
					</SheetHeader>

					{isLoadingClient ? (
						<div className="flex flex-1 flex-col items-center justify-center gap-8">
							<div className="flex flex-col items-center gap-4">
								<div className="h-8 w-8 animate-spin rounded-full border-2 border-[#005893] border-t-transparent"></div>
								<p className="text-sm text-gray-500">
									Loading patient details...
								</p>
							</div>
						</div>
					) : (
						<>
							<div className="flex flex-1 flex-col gap-6">
								<div className="space-y-5">
									<div className="space-y-2">
										<Label
											htmlFor="firstName"
											className="text-xs"
										>
											First Name *
										</Label>
										<Input
											id="firstName"
											name="firstName"
											value={formData.firstName}
											onChange={handleInputChange}
											className="h-9 text-xs"
											placeholder="Enter first name"
										/>
									</div>

									<div className="space-y-2">
										<Label
											htmlFor="lastName"
											className="text-xs"
										>
											Last Name *
										</Label>
										<Input
											id="lastName"
											name="lastName"
											value={formData.lastName}
											onChange={handleInputChange}
											className="h-9 text-xs"
											placeholder="Enter last name"
										/>
									</div>

									<div className="space-y-2">
										<Label
											htmlFor="email"
											className="text-xs"
										>
											Email Address *
										</Label>
										<Input
											id="email"
											name="email"
											type="email"
											value={formData.email}
											onChange={handleInputChange}
											className="h-9 text-xs"
											placeholder="Enter email address"
										/>
									</div>

									<div className="space-y-2">
										<Label
											htmlFor="phone"
											className="text-xs"
										>
											Phone Number *
										</Label>
										<div className="flex h-9 items-center rounded-md border border-gray-200 px-3">
											<div className="flex items-center gap-1.5">
												<div className="h-6 w-6 overflow-hidden rounded-full">
													<img
														src="https://placehold.co/24x24"
														alt="Flag"
														className="h-full w-full object-cover"
													/>
												</div>
												<span className="text-xs">
													+1
												</span>
											</div>
											<Input
												id="phone"
												name="phone"
												value={formData.phone}
												onChange={handleInputChange}
												className="h-full border-0 text-xs focus-visible:ring-0 focus-visible:ring-offset-0"
												placeholder="Enter phone number"
											/>
										</div>
									</div>

									{/* Business attributes fields */}
									{isLoadingAttributes ? (
										<div className="space-y-2">
											<div className="h-4 animate-pulse rounded bg-gray-200"></div>
											<div className="h-9 animate-pulse rounded bg-gray-200"></div>
										</div>
									) : (
										businessAttributes.map((attr) => (
											<div
												key={attr.id}
												className="space-y-2"
											>
												<Label
													htmlFor={attr.key}
													className="text-xs"
												>
													{attr.label}
													{attr.is_required && " *"}
												</Label>
												{attr.type === "checkbox" ? (
													<div className="flex items-center space-x-2">
														<input
															type="checkbox"
															id={attr.key}
															name={`customFields.${attr.key}`}
															checked={Boolean(
																formData
																	.customFields?.[
																	attr.key
																]
															)}
															onChange={
																handleInputChange
															}
															className="h-4 w-4"
														/>
														<Label
															htmlFor={attr.key}
															className="text-xs font-normal"
														>
															{attr.label}
														</Label>
													</div>
												) : (
													<Input
														id={attr.key}
														name={`customFields.${attr.key}`}
														type={
															attr.type ===
															"number"
																? "number"
																: "text"
														}
														value={String(
															formData
																.customFields?.[
																attr.key
															] || ""
														)}
														onChange={
															handleInputChange
														}
														className="h-9 text-xs"
														placeholder={`Enter ${attr.label.toLowerCase()}`}
														required={
															attr.is_required
														}
													/>
												)}
											</div>
										))
									)}
								</div>
							</div>

							<div className="flex w-full items-center justify-between gap-2.5 pt-7 pb-5">
								<Button
									variant="outline"
									onClick={handleClose}
									className="h-9"
								>
									Cancel
								</Button>
								<Button
									onClick={handleSubmit}
									disabled={updateClientMutation.isPending}
									className="h-9 bg-[#005893] hover:bg-[#004a7a]"
								>
									{updateClientMutation.isPending
										? "Saving..."
										: "Save"}
								</Button>
							</div>
						</>
					)}
				</div>
			</SheetContent>
		</Sheet>
	);
}
