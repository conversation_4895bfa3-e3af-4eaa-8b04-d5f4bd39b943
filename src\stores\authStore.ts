import { create } from "zustand";
import { persist, devtools } from "zustand/middleware";

interface User {
	id: string;
	email: string;
	name: string;
	role: "admin" | "staff" | "manager";
	locationIds: string[];
	permissions: string[];
}

interface Organisation {
	id: string;
	name: string;
	slug: string;
}

interface AuthState {
	// State
	user: User | null;
	token: string | null;
	refreshToken: string | null;
	isAuthenticated: boolean;
	lastLoginAt: Date | null;
	selectedOrganisation: Organisation | null;

	// Actions
	setAuth: (user: User, token: string, refreshToken: string) => void;
	logout: () => void;
	updateUser: (updates: Partial<User>) => void;
	setLastLogin: (date: Date) => void;
	setSelectedOrganisation: (organisation: Organisation | null) => void;

	// Computed values (getters)
	hasPermission: (permission: string) => boolean;
	canAccessLocation: (locationId: string) => boolean;
}

export const useAuthStore = create<AuthState>()(
	devtools(
		persist(
			(set, get) => ({
				// Initial state
				user: null,
				token: null,
				refreshToken: null,
				isAuthenticated: false,
				lastLoginAt: null,
				selectedOrganisation: null,

				// Actions
				setAuth: (user, token, refreshToken) => {
					set({
						user,
						token,
						refreshToken,
						isAuthenticated: true,
						lastLoginAt: new Date(),
					});
				},

				logout: () => {
					set({
						user: null,
						token: null,
						refreshToken: null,
						isAuthenticated: false,
						lastLoginAt: null,
						selectedOrganisation: null,
					});
				},

				updateUser: (updates) => {
					set((state) => ({
						user: state.user ? { ...state.user, ...updates } : null,
					}));
				},

				setLastLogin: (date) => set({ lastLoginAt: date }),

				setSelectedOrganisation: (organisation) => {
					set({ selectedOrganisation: organisation });
				},

				// Computed values
				hasPermission: (permission) => {
					const { user } = get();
					return user?.permissions.includes(permission) ?? false;
				},

				canAccessLocation: (locationId) => {
					const { user } = get();
					return user?.locationIds.includes(locationId) ?? false;
				},
			}),
			{
				name: "auth-store",
				partialize: (state) => ({
					// Only persist these fields
					user: state.user,
					token: state.token,
					refreshToken: state.refreshToken,
					isAuthenticated: state.isAuthenticated,
					lastLoginAt: state.lastLoginAt,
					selectedOrganisation: state.selectedOrganisation,
				}),
			}
		),
		{ name: "AuthStore" }
	)
);
