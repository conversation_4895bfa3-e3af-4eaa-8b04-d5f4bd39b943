import {
    Calendar as Big<PERSON>ale<PERSON><PERSON>,
    momentLocalizer, Views
} from "react-big-calendar";
import type { SlotInfo } from "react-big-calendar";
import moment from "moment";
import { useCallback, useRef, useState, useEffect, useLayoutEffect } from "react";
import { CustomEventComponent } from "./components/event";
import CustomToolbar from "./components/custom-toolbar";
import { CustomResourceHeader } from "./components/custom-resource-header";
import { getAppointmentColors } from "@/pages/schedules/utils/index";
import { CustomTimeSlotWrapper } from "./components/custom-time-slot";
import { generateAppointments, resources } from "./db";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuShortcut
} from "@/components/ui/dropdown-menu";
import { TbUserSquare } from "react-icons/tb";
import { PiBellRinging } from "react-icons/pi";
import clsx from "clsx";
import { Button } from "@/components/ui/Button/Button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { getWeekDays } from "@/pages/schedules/utils/index";
import ScheduleOptimizer from "./schedule-optimizer";
import BookAppointment from "./book-appointment";

type Keys = keyof typeof Views;

interface Appointment {
    title: string;
}

export default function Calendar() {
    const localizer = momentLocalizer(moment);
    const clickRef = useRef<number>(0);
    const lastMouseEvent = useRef<MouseEvent | null>(null);
    const [open, setOpen] = useState(false)
    const [date, setDate] = useState<Date | undefined>(undefined)
    const [view, setView] = useState<(typeof Views)[Keys]>(Views.DAY);
    const [optimizerOpen, setOptimizerOpen] = useState<boolean>(false);
    const [activeDoctor, setActiveDoctor] = useState<number>(0);
    const [arrowPosition, setArrowPosition] = useState(0);
    const [arrowWidth, setArrowWidth] = useState(0);
    const [appointmentOpen, setAppointmentOpen] = useState<boolean>(false);
    const [contextMenuInfo, setContextMenuInfo] = useState<{
        xPosition: number;
        yPosition: number;
        selectedTime: string;
        resourceId: number;
    }>();

    const resourceRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

    // Ref for resources (doctors) scroll container
    const resourcesScrollRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleMouseDown = (e: MouseEvent) => {
            lastMouseEvent.current = e;
        };
        document.addEventListener('mousedown', handleMouseDown);
        return () => {
            document.removeEventListener('mousedown', handleMouseDown);
        };
    }, []);

    useLayoutEffect(() => {
        const activeTabElement = resourceRefs.current[activeDoctor];
        if (activeTabElement) {
            setArrowPosition(activeTabElement.offsetLeft);
            setArrowWidth(activeTabElement.offsetWidth);
        }
    }, [activeDoctor, resources.length, view]);

    const events = generateAppointments({
        view: view === Views.WEEK ? 'week' : 'day',
        activeDoctor,
        date: date || new Date(),
    });

    const CustomEvent = ({ event }: { event: any }) => (
        <>
            {event.appointments.map((appt: Appointment, index: number, arr: Appointment[]) => {
                const appointmentColors = getAppointmentColors(arr.length)

                return <CustomEventComponent
                    key={index}
                    index={index}
                    start={event.start}
                    end={event.end}
                    title={appt.title}
                    arrLength={arr.length}
                    appointmentColors={appointmentColors}
                />
            }
            )}
        </>
    );

    const components = {
        resourceHeader: CustomResourceHeader,
        event: CustomEvent,
        timeSlotWrapper: CustomTimeSlotWrapper,
    }

    const onPrevClick = useCallback(() => {
        if (view === Views.DAY) {
            setDate(moment(date).subtract(1, "d").toDate());
        } else if (view === Views.WEEK) {
            setDate(moment(date).subtract(1, "w").toDate());
        } else {
            setDate(moment(date).subtract(1, "M").toDate());
        }
    }, [view, date]);

    const onNextClick = useCallback(() => {
        if (view === Views.DAY) {
            setDate(moment(date).add(1, "d").toDate());
        } else if (view === Views.WEEK) {
            setDate(moment(date).add(1, "w").toDate());
        } else {
            setDate(moment(date).add(1, "M").toDate());
        }
    }, [view, date]);

    const onSelectSlot = useCallback((slotInfo: SlotInfo) => {
        window.clearTimeout(clickRef?.current)
        clickRef.current = window.setTimeout(() => {
            const mouseEvent = lastMouseEvent.current;
            if (!mouseEvent) return;
            const selectedTime = moment(slotInfo.start).format('HH:mm');
            setContextMenuInfo({
                xPosition: mouseEvent.clientX,
                yPosition: mouseEvent.clientY,
                selectedTime,
                resourceId: typeof slotInfo.resourceId === 'number' ? slotInfo.resourceId : 0
            });
        }, 250)
    }, [])

    return (
        <div
            className="w-full !pr-4">
            <DropdownMenu open={!!contextMenuInfo} onOpenChange={() => setContextMenuInfo(undefined)}>
                <DropdownMenuContent
                    className="w-80" align="end"
                    style={{
                        position: 'fixed',
                        left: contextMenuInfo?.xPosition || 0,
                        top: contextMenuInfo?.yPosition || 0,
                        zIndex: 30000
                    }}>
                    <DropdownMenuLabel className="font-bold">Time Slot Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuGroup>
                        <DropdownMenuItem onClick={() => {
                            setAppointmentOpen(true);
                            setContextMenuInfo(undefined);
                        }}>
                            <TbUserSquare />
                            <p className="ml-1">Book an Appointment</p>
                            <DropdownMenuShortcut>
                                ⇧⌘P
                            </DropdownMenuShortcut>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => {
                            setContextMenuInfo(undefined);
                            setOptimizerOpen(true);
                        }}>
                            <PiBellRinging />
                            <p className="ml-1">Trigger Schedule Optimizer</p>
                            <DropdownMenuShortcut>⌘⇧C</DropdownMenuShortcut>
                        </DropdownMenuItem>

                    </DropdownMenuGroup>
                </DropdownMenuContent>
            </DropdownMenu>

            {view === Views.WEEK && (
                <style>
                    {`
                    .rbc-header, .rbc-time-header {
                        display: none;
                    }
                    .rbc-day-slot {
	                    min-width: 14rem !important;
                    }
                    .rbc-time-view.rbc-time-view-resources {
                        border-top-width: 0;
                    }
                    `}
                </style>
            )}

            <ScheduleOptimizer open={optimizerOpen} onOpenChange={setOptimizerOpen} />

            <BookAppointment open={appointmentOpen} onOpenChange={setAppointmentOpen} />

            <div>
                <div className={clsx("flex flex-col gap-y-2", view === Views.WEEK && "flex-col-reverse")}>

                    {view === Views.WEEK && (
                        <div>
                            <div className="flex items-center justify-between gap-x-2 mb-4 -mt-2">
                                <Button variant="outline" className="!px-1.5 !py-1.5 h-8 rounded-lg cursor-pointer"
                                    onClick={() => {
                                        resourcesScrollRef.current?.scrollBy({ left: -200, behavior: 'smooth' });
                                    }}
                                >
                                    <ChevronLeft style={{ width: "1rem", height: "1rem" }} />
                                </Button>
                                <div className="relative grid grid-flow-col gap-4 max-w-[100%] overflow-x-auto scrollbar-hide" ref={resourcesScrollRef}>
                                    {resources.map((resource, index) => (
                                        <button
                                            key={index}
                                            tabIndex={0}
                                            ref={(el) => {
                                                resourceRefs.current[index] = el;
                                            }}
                                            onClick={() => setActiveDoctor(index)}
                                            className={clsx("cursor-pointer relative min-w-[10rem] whitespace-nowrap p-2 px-3 border border-transparent transition-all duration-300 ease-in-out", activeDoctor === index && "bg-[#0058930A]")}
                                        >
                                            <p className="font-medium">{resource.resourceTitle}</p>
                                        </button>
                                    ))}
                                    <div className="absolute bottom-0 left-0 w-full h-[2.4px] bg-[#005893] transition-all duration-300 ease-in-out" style={{
                                        left: arrowPosition,
                                        width: arrowWidth,
                                    }} />
                                </div>
                                <Button variant="outline" className="!px-1.5 !py-1.5 h-8 rounded-lg cursor-pointer"
                                    onClick={() => {
                                        resourcesScrollRef.current?.scrollBy({ left: 200, behavior: 'smooth' });
                                    }}
                                >
                                    <ChevronRight style={{ width: "1rem", height: "1rem" }} />
                                </Button>
                            </div>
                            <div className="grid grid-flow-col gap-4 max-w-[100%] overflow-x-auto scrollbar-hide">
                                <div className="w-[4.2rem]"></div>
                                {getWeekDays(date || new Date()).map((d, i) => {
                                    const dayName = d.toLocaleDateString('en-US', { weekday: 'short' });
                                    return (
                                        <div className="w-[14rem] grid grid-flow-col place-content-center py-3" key={i}>
                                            <div className={clsx("px-3 py-1 rounded-md font-semibold text-[15px]", d.getDate() === new Date().getDate() ? "bg-[#00589329]" : "")}> 
                                                <span className="mr-1">{dayName}</span>
                                                <span>{d.getDate()}</span>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    )}

                    <CustomToolbar
                        onPrevClick={onPrevClick}
                        onNextClick={onNextClick}
                        date={date || new Date()}
                        view={view}
                        setDate={(date: Date) => setDate(date)}
                        setView={(view: string) => setView(view as (typeof Views)[Keys])}
                        open={open}
                        setOpen={setOpen}
                    />
                </div>

                <BigCalendar
                    selectable={true}
                    onSelectSlot={onSelectSlot}
                    events={events}
                    localizer={localizer}
                    resources={view === Views.DAY ? resources : undefined}
                    resourceIdAccessor="resourceId"
                    resourceTitleAccessor="resourceTitle"
                    defaultView={Views.DAY}
                    components={{
                        ...components,
                    }}
                    view={view}
                    date={date}
                    views={['day', 'week', 'month']}
                    style={{ height: "100%", width: "100%" }}
                    startAccessor="start"
                    endAccessor="end"
                    toolbar={false}
                />
            </div>
        </div >
    )
}
