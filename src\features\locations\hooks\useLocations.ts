import { useQuery } from "@tanstack/react-query";
import { locationsApi } from "../api";
import { queryKeys } from "@/lib/query/keys";
import type { LocationsFilters } from "../types";

export const useLocations = (filters: LocationsFilters = {}) => {
	return useQuery({
		queryKey: queryKeys.locations.list(filters),
		queryFn: () => locationsApi.getLocations(filters),
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
};
