import { X, Phone, Mail, Calendar, MapPin, Shield } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>et<PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";

interface TeamMember {
	id: string;
	name: string;
	email: string;
	phone: string;
	role: string;
	status: "Active" | "Unverified" | "Pending";
	dateOnboarded: string;
	avatar?: string;
	// Additional details for view
	socialRoles?: string[];
	roleDetails?: {
		[key: string]: string[];
	};
	locationAccess?: string[];
	timeZone?: string;
	autoMessage?: string;
	serviceManager?: string;
}

interface ViewMemberSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	member: TeamMember | null;
	onEdit?: (member: TeamMember) => void;
}

export function ViewMemberSheet({
	open,
	onOpen<PERSON>hang<PERSON>,
	member,
	onEdit,
}: ViewMemberSheetProps) {
	if (!member) return null;

	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case "Active":
				return "default";
			case "Unverified":
				return "secondary";
			case "Pending":
				return "outline";
			default:
				return "default";
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "Active":
				return "border-green-200 bg-green-100 text-green-800 hover:bg-green-100";
			case "Unverified":
				return "border-yellow-200 bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
			case "Pending":
				return "bg-gray-100 text-gray-800 hover:bg-gray-100";
			default:
				return "bg-gray-100 text-gray-800 hover:bg-gray-100";
		}
	};

	// Mock additional data for demonstration
	const memberDetails = {
		...member,
		socialRoles: ["Station Manager", "Team Member"],
		roleDetails: {
			"Station Manager": ["Station Alpha", "Station Beta"],
		},
		locationAccess: ["University of Waterloo, Ontario Laboratory"],
		timeZone: "EST (UTC-5)",
		autoMessage: "Thank you for booking with us. I'll be with you shortly!",
		serviceManager: "Janet Samuel",
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-[500px] overflow-y-auto px-8 py-10 sm:max-w-[500px]">
				<SheetHeader className="pb-6">
					<div className="flex items-center justify-between">
						<SheetTitle className="text-xl font-semibold">
							Member Details
						</SheetTitle>
					</div>
				</SheetHeader>

				<div className="space-y-6">
					{/* Profile Info */}
					<div className="space-y-4">
						<h3 className="text-lg font-medium">Profile Info</h3>

						{/* Two-column grid layout for profile info */}
						<div className="grid grid-cols-2 gap-6">
							{/* Full Name */}
							<div>
								<p className="mb-1 text-sm font-medium text-gray-700">
									Full Name
								</p>
								<p className="text-sm font-medium text-gray-900">
									{member.name}
								</p>
							</div>

							{/* Email Address */}
							<div>
								<p className="mb-1 text-sm font-medium text-gray-700">
									Email Address
								</p>
								<p className="text-sm text-gray-600">
									{member.email}
								</p>
							</div>

							{/* Phone Number */}
							<div>
								<p className="mb-1 text-sm font-medium text-gray-700">
									Phone Number
								</p>
								<p className="text-sm text-gray-600">
									{member.phone}
								</p>
							</div>

							{/* Date Onboarded */}
							<div>
								<p className="mb-1 text-sm font-medium text-gray-700">
									Date Onboarded
								</p>
								<p className="text-sm text-gray-600">
									{member.dateOnboarded}
								</p>
							</div>
						</div>
					</div>

					{/* Applied Role */}
					<div className="space-y-4">
						<h3 className="text-lg font-medium">Applied Role</h3>

						{/* Station Manager Role */}
						<div className="space-y-4">
							<div className="rounded-md border border-gray-200 p-4">
								<h4 className="mb-3 text-base font-medium text-gray-900">
									Station Manager
								</h4>

								{/* Location 1 */}
								<div className="mb-4 space-y-3">
									<div className="flex items-center gap-2">
										<MapPin className="h-4 w-4 text-gray-500" />
										<p className="text-sm font-medium text-gray-700">
											Location Name Here
										</p>
									</div>
									<div className="grid grid-cols-4 gap-2">
										<div className="rounded bg-gray-100 px-1 py-1 text-center">
											<p className="text-xs text-gray-600">
												Station Name
											</p>
										</div>
										<div className="rounded bg-gray-100 px-1 py-1 text-center">
											<p className="text-xs text-gray-600">
												Station Name
											</p>
										</div>
										<div className="rounded bg-gray-100 px-1 py-1 text-center">
											<p className="text-xs text-gray-600">
												Station Name
											</p>
										</div>
										<div className="rounded bg-gray-100 px-1 py-1 text-center">
											<p className="text-xs text-gray-600">
												Station Name
											</p>
										</div>
									</div>
								</div>

								{/* Location 2 */}
								<div className="space-y-3">
									<div className="flex items-center gap-2">
										<MapPin className="h-4 w-4 text-gray-500" />
										<p className="text-sm font-medium text-gray-700">
											Station Name Here
										</p>
									</div>
									<div className="grid grid-cols-1 gap-2">
										<div className="rounded bg-gray-100 px-2 py-1 text-center">
											<p className="text-xs text-gray-600">
												Station Name
											</p>
										</div>
									</div>
								</div>
							</div>

							{/* Team Member Role */}
							<div className="rounded-md border border-gray-200 p-4">
								<h4 className="mb-3 text-base font-medium text-gray-900">
									Team Member
								</h4>

								<div className="space-y-3">
									<div className="flex items-center gap-2">
										<MapPin className="h-4 w-4 text-gray-500" />
										<p className="text-sm font-medium text-gray-700">
											Location Name Here
										</p>
									</div>
									<div className="grid grid-cols-3 gap-2">
										<div className="rounded bg-gray-100 px-2 py-1 text-center">
											<p className="text-xs text-gray-600">
												Station Name
											</p>
										</div>
										<div className="rounded bg-gray-100 px-2 py-1 text-center">
											<p className="text-xs text-gray-600">
												Station Name
											</p>
										</div>
										<div className="rounded bg-gray-100 px-2 py-1 text-center">
											<p className="text-xs text-gray-600">
												Station Name
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Footer Actions */}
				<div className="flex justify-end gap-3 border-t pt-6">
					<Button
						variant="outline"
						onClick={() => onOpenChange(false)}
					>
						Close
					</Button>
					{onEdit && (
						<Button
							onClick={() => {
								onEdit(member);
								onOpenChange(false);
							}}
							className="bg-blue-600 hover:bg-blue-700"
						>
							Edit Member
						</Button>
					)}
				</div>
			</SheetContent>
		</Sheet>
	);
}
