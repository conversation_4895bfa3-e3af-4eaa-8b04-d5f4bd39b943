import { useQuery } from "@tanstack/react-query";
import { businessAttributesApi } from "@/lib/api/businessAttributes";
import { queryKeys, mediumLivedQueryOptions } from "@/lib/query";
import type { BusinessAttributesFilters } from "@/types/businessAttributes";

export const useBusinessAttributes = (
	filters: BusinessAttributesFilters = {},
	options?: {
		enabled?: boolean;
	}
) => {
	return useQuery({
		queryKey: queryKeys.businessAttributes.list(filters),
		queryFn: () => businessAttributesApi.getBusinessAttributes(filters),
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false,
	});
};

export const useBusinessAttributesForForm = (options?: {
	enabled?: boolean;
}) => {
	const filters: BusinessAttributesFilters = {
		page: 1,
		per_page: 100,
		show_in_list: true,
	};

	return useBusinessAttributes(filters, options);
};
