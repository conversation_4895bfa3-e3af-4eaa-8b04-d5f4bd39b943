import useCustomToast from "@/components/CustomToast";

import type { AuthTwoEnabledFactorResponse } from "@/types/signup";
import {
	ROUTES,
} from "@/lib/utils/constants"
import { setCookie } from "@/lib/utils/cookies";
import useUserStore from "@/stores/useUserStore";
import type { LoginResponse } from "@/types/api/auth";
import { useNavigate } from "react-router";

export const useHandleLoginSuccess = () => {
	const navigate = useNavigate();
	const customToast = useCustomToast();
	const rememberAuth = useUserStore((s: any) => s.rememberAuth);
	const setUser = useUserStore((s: any) => s.setUser);
	const setMfaUser = useUserStore((s: any) => s.setMfaUser);
	const setRememberAuth = useUserStore((s: any) => s.setRememberAuth);

	return (data: LoginResponse | AuthTwoEnabledFactorResponse) => {
		if ("twoFactor" in data.data) {
			setMfaUser(data as AuthTwoEnabledFactorResponse);
			setRememberAuth({
				rememberMe: rememberAuth?.rememberMe,
				rememberToken: data.data.token,
			});
			return navigate(ROUTES.AUTH.MFA);
		}

		if (!data.data.is_email_verified) {
			setUser(data.data);
			navigate(`${ROUTES.AUTH.SIGNIN}?showVerifyEmail=true&email=${encodeURIComponent(data.data.email)}`);
			customToast("Authentication successful! Please verify your email to continue.", {
				id: "auth-success-email-verify",
				type: "success",
			});
			return;
		}

		setCookie("ac-token", data.data.token, 7);
		setUser(data.data);
		customToast("Login successful 🎉", {
			id: "login",
		});
		
		if (data.data.remember_token && rememberAuth?.rememberMe) {
			setRememberAuth({
				rememberMe: rememberAuth?.rememberMe,
				rememberToken: data.data.remember_token,
			});
		}

		navigate(ROUTES.HOME);
	};
};
