import { apiClient } from "@/lib/api/clients";
import type {
	Location,
	LocationsResponse,
	LocationsFilters,
	CreateLocationRequest,
	UpdateLocationRequest,
} from "../types";

const LOCATIONS_ENDPOINTS = {
	base: "/api/locations",
	byId: (id: string) => `/api/locations/${id}`,
} as const;

export const locationsApi = {
	// Get all locations with filters
	getLocations: async (
		filters: LocationsFilters = {}
	): Promise<LocationsResponse> => {
		const params = new URLSearchParams();

		Object.entries(filters).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				if (Array.isArray(value)) {
					value.forEach((item) =>
						params.append(key, item.toString())
					);
				} else {
					params.append(key, value.toString());
				}
			}
		});

		const response = await apiClient.get(
			`${LOCATIONS_ENDPOINTS.base}?${params}`
		);
		return response.data;
	},

	// Get single location by ID
	getLocation: async (id: string): Promise<Location> => {
		const response = await apiClient.get(LOCATIONS_ENDPOINTS.byId(id));
		return response.data;
	},

	// Create new location
	createLocation: async (data: CreateLocationRequest): Promise<Location> => {
		const response = await apiClient.post(LOCATIONS_ENDPOINTS.base, data);
		return response.data;
	},

	// Update existing location
	updateLocation: async (data: UpdateLocationRequest): Promise<Location> => {
		const { id, ...updateData } = data;
		const response = await apiClient.put(
			LOCATIONS_ENDPOINTS.byId(id),
			updateData
		);
		return response.data;
	},

	// Delete location
	deleteLocation: async (id: string): Promise<void> => {
		await apiClient.delete(LOCATIONS_ENDPOINTS.byId(id));
	},

	// Toggle location active status
	toggleLocationStatus: async (
		id: string,
		isActive: boolean
	): Promise<Location> => {
		const response = await apiClient.patch(LOCATIONS_ENDPOINTS.byId(id), {
			isActive,
		});
		return response.data;
	},
};
