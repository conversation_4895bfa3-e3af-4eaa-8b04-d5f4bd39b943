import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Search, ChevronDown, ChevronUp } from "lucide-react";

export interface LocationSelectionData {
	applyToAll: boolean;
	selectedLocations: string[];
	selectedStations: Record<string, string[]>; 
}

export interface LocationData {
	id: string;
	name: string;
	stations: StationData[];
	expanded?: boolean;
}

export interface StationData {
	id: string;
	name: string;
}

interface LocationSelectionStepProps {
	locationData: LocationSelectionData;
	onLocationDataChange: (data: LocationSelectionData) => void;
}

export function LocationSelectionStep({
	locationData,
	onLocationDataChange,
}: LocationSelectionStepProps) {
	const [locations, setLocations] = useState<LocationData[]>([
		{
			id: "1",
			name: "Downtown Medical Center",
			expanded: false,
			stations: [
				{ id: "1-1", name: "Reception Desk" },
				{ id: "1-2", name: "Consultation Room A" },
				{ id: "1-3", name: "Consultation Room B" },
				{ id: "1-4", name: "Laboratory" },
			],
		},
		{
			id: "2",
			name: "Westside Clinic",
			expanded: false,
			stations: [
				{ id: "2-1", name: "Front Desk" },
				{ id: "2-2", name: "Examination Room" },
			],
		},
		{
			id: "3",
			name: "University Health Center",
			expanded: true,
			stations: [
				{ id: "3-1", name: "Student Services" },
				{ id: "3-2", name: "Mental Health Wing" },
				{ id: "3-3", name: "General Practice" },
				{ id: "3-4", name: "Pharmacy" },
			],
		},
		{
			id: "4",
			name: "Northside Family Practice",
			expanded: false,
			stations: [
				{ id: "4-1", name: "Pediatric Unit" },
				{ id: "4-2", name: "Adult Care Unit" },
			],
		},
	]);

	const handleLocationToggle = (locationId: string) => {
		const isSelected = locationData.selectedLocations.includes(locationId);
		if (isSelected) {
			onLocationDataChange({
				...locationData,
				selectedLocations: locationData.selectedLocations.filter(
					(id) => id !== locationId
				),
				selectedStations: {
					...locationData.selectedStations,
					[locationId]: [],
				},
			});
		} else {
			onLocationDataChange({
				...locationData,
				selectedLocations: [
					...locationData.selectedLocations,
					locationId,
				],
			});
		}
	};

	const handleStationToggle = (locationId: string, stationId: string) => {
		const currentStations = locationData.selectedStations[locationId] || [];
		const isSelected = currentStations.includes(stationId);

		if (isSelected) {
			onLocationDataChange({
				...locationData,
				selectedStations: {
					...locationData.selectedStations,
					[locationId]: currentStations.filter(
						(id) => id !== stationId
					),
				},
			});
		} else {
			onLocationDataChange({
				...locationData,
				selectedStations: {
					...locationData.selectedStations,
					[locationId]: [...currentStations, stationId],
				},
			});
		}
	};

	const toggleLocationExpansion = (locationId: string) => {
		setLocations(
			locations.map((loc) =>
				loc.id === locationId
					? { ...loc, expanded: !loc.expanded }
					: loc
			)
		);
	};

	return (
		<div className="flex flex-col gap-6">
			<div className="flex flex-col gap-4">
				<h3 className="text-sm font-semibold">Apply categories to</h3>
				<div className="flex items-start gap-2 rounded-md border border-gray-200 bg-white p-3">
					<div className="relative mt-0.5 flex h-3 w-3 items-center justify-center rounded-full border border-[#005893] bg-white">
						{locationData.applyToAll && (
							<div className="h-1.5 w-1.5 rounded-full bg-[#005893]" />
						)}
					</div>
					<button
						onClick={() =>
							onLocationDataChange({
								...locationData,
								applyToAll: true,
							})
						}
						className="flex flex-col gap-1.5 text-left"
					>
						<div className="text-xs font-medium">
							All Locations and Stations
						</div>
						<div className="text-xs text-gray-500">
							This will add this Client to all locations &
							stations in this location.
						</div>
					</button>
				</div>
				<div className="flex items-start gap-2 rounded-md border border-gray-200 bg-white p-3">
					<div className="relative mt-0.5 flex h-3 w-3 items-center justify-center rounded-full border border-[#005893] bg-white">
						{!locationData.applyToAll && (
							<div className="h-1.5 w-1.5 rounded-full bg-[#005893]" />
						)}
					</div>
					<button
						onClick={() =>
							onLocationDataChange({
								...locationData,
								applyToAll: false,
							})
						}
						className="flex flex-col gap-1.5 text-left"
					>
						<div className="text-xs font-medium">
							Selected Locations and Stations only
						</div>
						<div className="text-xs text-gray-500">
							This will add this Client to all locations &
							stations in this location.
						</div>
					</button>
				</div>
			</div>

			{!locationData.applyToAll && (
				<div className="flex w-[477px] flex-col gap-3 border-t border-gray-200 pt-3">
					<div className="relative flex h-9 items-center gap-2 overflow-hidden rounded-md border border-gray-200 bg-white p-3">
						<Search className="h-3 w-3 opacity-50" />
						<input
							type="text"
							placeholder="Search locations and stations..."
							className="flex-1 border-none bg-transparent text-xs text-gray-900 placeholder-gray-500 outline-none"
						/>
					</div>
					<div className="flex flex-col gap-2">
						{locations.map((location) => (
							<div key={location.id}>
								{!location.expanded && (
									<div className="flex h-14 items-center rounded-md border border-gray-200 bg-white">
										<div className="flex items-center gap-2.5 px-4">
											<Checkbox
												checked={locationData.selectedLocations.includes(
													location.id
												)}
												onCheckedChange={() =>
													handleLocationToggle(
														location.id
													)
												}
												className="h-4 w-4"
											/>
										</div>
										<div className="flex min-w-20 flex-1 items-center gap-3 px-3">
											<div className="text-sm font-medium">
												{location.name}
											</div>
										</div>
										<div className="flex min-w-16 items-center justify-end gap-1.5 px-3">
											<Button
												variant="ghost"
												size="sm"
												className="h-6 w-6 rounded-md p-2"
												onClick={() =>
													toggleLocationExpansion(
														location.id
													)
												}
											>
												<ChevronDown className="h-3 w-3 text-blue-600" />
											</Button>
										</div>
									</div>
								)}
								{location.expanded && (
									<div className="flex flex-col overflow-hidden rounded-md border border-gray-200">
										<div className="flex h-14 items-center border-t border-gray-200 bg-white">
											<div className="flex items-center gap-2.5 px-4">
												<Checkbox
													checked={locationData.selectedLocations.includes(
														location.id
													)}
													onCheckedChange={() =>
														handleLocationToggle(
															location.id
														)
													}
													className="h-4 w-4"
												/>
											</div>
											<div className="flex min-w-20 flex-1 items-center gap-3 px-3">
												<div className="text-sm font-medium">
													{location.name}
												</div>
											</div>
											<div className="flex min-w-16 items-center justify-end gap-1.5 px-3">
												<Button
													variant="ghost"
													size="sm"
													className="h-6 w-6 rounded-md p-2"
													onClick={() =>
														toggleLocationExpansion(
															location.id
														)
													}
												>
													<ChevronUp className="h-3 w-3" />
												</Button>
											</div>
										</div>
										<div className="flex flex-col bg-white pl-9">
											{location.stations.map(
												(station) => (
													<div
														key={station.id}
														className="flex h-9 items-center bg-white"
													>
														<div className="flex items-center gap-2.5 px-4">
															<Checkbox
																checked={(
																	locationData
																		.selectedStations[
																		location
																			.id
																	] || []
																).includes(
																	station.id
																)}
																onCheckedChange={() =>
																	handleStationToggle(
																		location.id,
																		station.id
																	)
																}
																className="h-4 w-4"
															/>
														</div>
														<div className="flex min-w-20 flex-1 items-center gap-3 px-3">
															<div className="text-sm font-normal">
																{station.name}
															</div>
														</div>
														<div className="w-16 min-w-16 px-3" />
													</div>
												)
											)}
										</div>
									</div>
								)}
							</div>
						))}
					</div>
				</div>
			)}
		</div>
	);
}
