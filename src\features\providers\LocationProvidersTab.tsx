import { useEffect, useState } from "react";
import { Search, Filter, Plus, MapPin, Settings2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";
import {
	AddLocationSheet,
	EditStationInformationSheet,
} from "../locations/components/sheets";
import { useLocations } from "../locations/hooks/useLocations";
import { LocationProviderCard } from "./components/LocationProviderCard";
import type {
	LocationsFilters,
	LocationsResponse,
	Location,
	CreateStationRequest,
} from "../locations/types";
import { SendBookingLinkSheet } from "@/features/schedule";
import { StationInformationSheet } from "../locations/components/sheets/station-information";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AddStationSheet } from "../locations/components/sheets";
import { useNavigate } from "react-router";

interface LocationProvidersTabProps {
	className?: string;
}

export function LocationProvidersTab({ className }: LocationProvidersTabProps) {
	const navigate = useNavigate();
	const [filters, setFilters] = useState<LocationsFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});
	const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [showAddLocationForm, setShowAddLocationForm] = useState(false);
	const [showLocationDetails, setShowLocationDetails] = useState(false);
	const [
		showEditStationInformationSheet,
		setShowEditStationInformationSheet,
	] = useState(false);
	const [selectedLocation, setSelectedLocation] = useState<Location | null>(
		null
	);
	const [showSendBookingLinkSheet, setShowSendBookingLinkSheet] =
		useState(false);
	const [activeTab, setActiveTab] = useState("list");
	const { data: locationsData2, isLoading } = useLocations({
		page: currentPage,
		search: searchTerm,
		limit: 10,
	});

	// Debounced search filter
	useEffect(() => {
		const timer = setTimeout(() => {
			setFilters((prev) => ({
				...prev,
				search: searchTerm || undefined,
				page: 1,
			}));
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	const locationsData: LocationsResponse = {
		data: [
			{
				id: "1",
				name: "Dr. John Doe",
				address: {
					street: "5 Park Home Ave #130",
					city: "Toronto",
					state: "CA",
					zipCode: "12345",
					country: "USA",
				},
				phone: "************",
				email: "<EMAIL>",
				description: "This is a description of Location 1",
				isActive: true,
				timezone: "America/New_York",
				coordinates: {
					latitude: 40.7128,
					longitude: -74.006,
				},
				operatingHours: [],
				services: [],
				capacity: 100,
				amenities: [],
				organizationId: "1",
				createdAt: "2021-01-01",
				updatedAt: "2021-01-01",
			},
			{
				id: "2",
				name: "University of Toronto, Ontario Research Centre",
				address: {
					street: "5 Park Home Ave #130",
					city: "Toronto",
					state: "CA",
					zipCode: "12345",
					country: "USA",
				},
				phone: "************",
				email: "<EMAIL>",
				description: "This is a description of Location 1",
				isActive: true,
				timezone: "America/New_York",
				coordinates: {
					latitude: 40.7128,
					longitude: -74.006,
				},
				operatingHours: [],
				services: [],
				capacity: 100,
				amenities: [],
				organizationId: "1",
				createdAt: "2021-01-01",
				updatedAt: "2021-01-01",
			},
		],
		pagination: {
			page: 1,
			limit: 12,
			total: 2,
			totalPages: 1,
		},
	};

	const handleFilterChange = (newFilters: Partial<LocationsFilters>) => {
		setFilters((prev) => ({ ...prev, ...newFilters, page: 1 }));
	};

	const handleSelectAll = (checked: boolean) => {
		if (checked && locationsData?.data) {
			setSelectedLocations(
				locationsData.data.map((location) => location.id)
			);
		} else {
			setSelectedLocations([]);
		}
	};

	const handleLocationSelection = (locationId: string, selected: boolean) => {
		if (selected) {
			setSelectedLocations((prev) => [...prev, locationId]);
		} else {
			setSelectedLocations((prev) =>
				prev.filter((id) => id !== locationId)
			);
		}
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleAddLocation = async (
		data: CreateStationRequest & { images?: File[] }
	) => {
		console.log("Adding new location:", data);
		// Here you would typically call your API to create the location
		// For now, we'll just log the data and close the form
		setShowAddLocationForm(false);
	};

	const handleViewLocation = (location: Location) => {
		navigate(
			`/dashboard/workplace/providers/provider-details/${location.id}`
		);
		// setSelectedLocation(location);
		// setShowLocationDetails(true);
	};

	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-between py-1">
				<Tabs value={activeTab} onValueChange={setActiveTab}>
					<TabsList className="bg-zinc-100">
						<TabsTrigger
							value="grid"
							className="cursor-pointer text-sm font-medium"
						>
							Grid View
						</TabsTrigger>
						<TabsTrigger
							value="list"
							className="cursor-pointer text-sm font-medium"
						>
							List View
						</TabsTrigger>
						<TabsTrigger
							value="tabs"
							className="cursor-pointer text-sm font-medium"
						>
							Tabs Text
						</TabsTrigger>
					</TabsList>
				</Tabs>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search locations..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className="cursor-pointer"
						size="icon"
						onClick={() => setShowAddLocationForm(true)}
					>
						<Settings2 className="h-4 w-4" />
					</Button>
					<Button
						variant="default"
						className="cursor-pointer"
						onClick={() => setShowAddLocationForm(true)}
					>
						<Plus className="mr-2 h-4 w-4" />
						Add Provider
					</Button>
				</div>
			</div>
			<div className="border-foregound-muted mt-5 box-border overflow-hidden rounded-lg border">
				<div className="flex items-center justify-between border-b border-zinc-200 py-4 pl-4">
					<Checkbox
						label=""
						checked={
							selectedLocations.length ===
							locationsData?.data?.length &&
							locationsData?.data?.length > 0
						}
						className="cursor-pointer"
						onCheckedChange={handleSelectAll}
					/>
				</div>

				{/* Locations Grid */}
				{locationsData && (
					<>
						{locationsData?.data?.length === 0 ? (
							<div className="py-12 text-center">
								<MapPin className="mx-auto h-12 w-12 text-gray-400" />
								<h3 className="mt-2 text-sm font-medium text-gray-900">
									No locations found
								</h3>
								<p className="mt-1 text-sm text-gray-500">
									Get started by creating your first location.
								</p>
								<Button
									className="mt-4"
									onClick={() => setShowAddLocationForm(true)}
								>
									<Plus className="mr-2 h-4 w-4" />
									Add Location
								</Button>
							</div>
						) : (
							<div className="flex flex-col gap-0.5">
								{locationsData?.data?.map(
									(locationProvider: any) => (
										<LocationProviderCard
											key={locationProvider.id}
											locationProvider={locationProvider}
											isSelected={selectedLocations.includes(
												locationProvider.id
											)}
											onSelectionChange={(selected) =>
												handleLocationSelection(
													locationProvider.id,
													selected
												)
											}
											onEdit={() =>
												setShowEditStationInformationSheet(
													true
												)
											}
											onView={() =>
												handleViewLocation(
													locationProvider
												)
											}
										/>
									)
								)}
							</div>
						)}

						{/* Pagination */}
						{locationsData?.pagination?.totalPages > 1 && (
							<div className="mt-8 flex items-center justify-center gap-2">
								<Button
									variant="outline"
									disabled={
										locationsData?.pagination?.page === 1
									}
									onClick={() =>
										handlePageChange(
											locationsData?.pagination?.page - 1
										)
									}
								>
									Previous
								</Button>

								<span className="text-sm text-gray-600">
									Page {locationsData.pagination.page} of{" "}
									{locationsData?.pagination?.totalPages}
								</span>

								<Button
									variant="outline"
									disabled={
										locationsData?.pagination?.page ===
										locationsData?.pagination?.totalPages
									}
									onClick={() =>
										handlePageChange(
											locationsData?.pagination?.page + 1
										)
									}
								>
									Next
								</Button>
							</div>
						)}
					</>
				)}
			</div>

			{/* Add Location Sheet */}
			<AddStationSheet
				open={showAddLocationForm}
				onOpenChange={setShowAddLocationForm}
				onSubmit={handleAddLocation}
			/>

			{/* Station Information Sheet */}
			<StationInformationSheet
				open={showLocationDetails}
				onClose={() => setShowLocationDetails(false)}
				onSendBookingLink={() => setShowSendBookingLinkSheet(true)}
			/>

			{/* Send Booking Link Sheet */}
			<SendBookingLinkSheet
				open={showSendBookingLinkSheet}
				onOpenChange={setShowSendBookingLinkSheet}
			/>

			{/* Edit Station Information Sheet */}
			<EditStationInformationSheet
				open={showEditStationInformationSheet}
				onClose={() => setShowEditStationInformationSheet(false)}
				stationId="station-123"
				stationData={selectedLocation as any}
				onSave={async () => { }}
				onProviderEdit={() => { }}
				onProviderDelete={() => { }}
				onAddProvider={() => { }}
			/>
		</div>
	);
}
