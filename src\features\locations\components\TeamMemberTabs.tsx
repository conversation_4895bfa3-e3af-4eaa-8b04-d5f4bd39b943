import { useEffect, useState } from "react";
import {
	Search,
	Plus,
	Users,
	Upload,
	Settings2,
	Phone,
	Mail,
	MoreHorizontal,
	Edit,
	Eye,
	Trash2,
	Pen,
	Info,
	Funnel,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AddMemberSheet } from "./sheets/AddMemberSheet";
import { ViewMemberSheet } from "./sheets/ViewMemberSheet";
import { EditMemberSheet } from "./sheets/EditMemberSheet";

interface TeamMember {
	id: string;
	name: string;
	email: string;
	phone: string;
	role: string;
	status: "Active" | "Unverified" | "Pending";
	dateOnboarded: string;
	avatar?: string;
	// Additional details for editing
	socialRoles?: string[];
	roleDetails?: {
		[key: string]: string[];
	};
	locationAccess?: string[];
	timeZone?: string;
	autoMessage?: string;
	serviceManager?: string;
}

interface TeamMembersTabProps {
	className?: string;
}

interface TeamMembersFilters {
	page: number;
	limit: number;
	search?: string;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
	status?: string;
	role?: string | string[];
}

interface TeamMembersResponse {
	data: TeamMember[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}

export function TeamMembersTab({ className }: TeamMembersTabProps) {
	const [filters, setFilters] = useState<TeamMembersFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});
	const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [showAddMemberForm, setShowAddMemberForm] = useState(false);
	const [showFilterSheet, setShowFilterSheet] = useState(false);
	const [showMemberDetails, setShowMemberDetails] = useState(false);
	const [showEditMember, setShowEditMember] = useState(false);
	const [selectedMember, setSelectedMember] = useState<TeamMember | null>(
		null
	);

	// Team Members data matching the image
	const teamMembersData: TeamMembersResponse = {
		data: [
			{
				id: "1",
				name: "Janet Samuel",
				email: "<EMAIL>",
				phone: "****** 94999",
				role: "Location Manager",
				status: "Active",
				dateOnboarded: "22 Oct 2020, 11:40 PM",
				avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b5c4?w=40&h=40&fit=crop&crop=face",
				socialRoles: ["Location Manager"],
				roleDetails: {
					"Location Manager": [
						"University of Waterloo, Ontario Laboratory",
					],
				},
				locationAccess: ["University of Waterloo, Ontario Laboratory"],
				timeZone: "EST (UTC-5)",
				autoMessage: "Welcome to our clinic! I'll assist you today.",
				serviceManager: "janet-samuel",
			},
			{
				id: "2",
				name: "Michael Johnson",
				email: "<EMAIL>",
				phone: "****** 12345",
				role: "Team Member",
				status: "Active",
				dateOnboarded: "15 Nov 2021, 09:30 AM",
				avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face",
				socialRoles: ["Team Member"],
				roleDetails: {},
				locationAccess: ["University of Waterloo, Ontario Laboratory"],
				timeZone: "EST (UTC-5)",
				autoMessage: "",
				serviceManager: "",
			},
			{
				id: "3",
				name: "Emma Watson",
				email: "<EMAIL>",
				phone: "****** 56432",
				role: "Team Member",
				status: "Unverified",
				dateOnboarded: "-",
				avatar: undefined, // No avatar for unverified member
				socialRoles: ["Team Member"],
				roleDetails: {},
				locationAccess: [],
				timeZone: "EST (UTC-5)",
				autoMessage: "",
				serviceManager: "",
			},
			{
				id: "4",
				name: "Chris Evans",
				email: "<EMAIL>",
				phone: "****** 87654",
				role: "Station Manager",
				status: "Active",
				dateOnboarded: "19 Mar 2023, 08:20 AM",
				avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face",
				socialRoles: ["Station Manager"],
				roleDetails: {
					"Station Manager": ["Station Alpha", "Station Beta"],
				},
				locationAccess: ["University of Waterloo, Ontario Laboratory"],
				timeZone: "EST (UTC-5)",
				autoMessage: "Happy to help with your station needs!",
				serviceManager: "",
			},
			{
				id: "5",
				name: "Sarah Lee",
				email: "<EMAIL>",
				phone: "****** 98765",
				role: "Organization Manager",
				status: "Active",
				dateOnboarded: "02 Jan 2023, 03:15 PM",
				avatar: "https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=40&h=40&fit=crop&crop=face",
				socialRoles: ["Organization Manager"],
				roleDetails: {},
				locationAccess: [
					"University of Waterloo, Ontario Laboratory",
					"University of Toronto, Ontario Research Centre",
				],
				timeZone: "EST (UTC-5)",
				autoMessage:
					"Welcome! I'm here to ensure everything runs smoothly.",
				serviceManager: "",
			},
			{
				id: "6",
				name: "David Chen",
				email: "<EMAIL>",
				phone: "****** 43210",
				role: "Team Member",
				status: "Active",
				dateOnboarded: "10 Feb 2022, 01:00 PM",
				avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=40&h=40&fit=crop&crop=face",
				socialRoles: ["Team Member"],
				roleDetails: {},
				locationAccess: [
					"University of Toronto, Ontario Research Centre",
				],
				timeZone: "EST (UTC-5)",
				autoMessage: "",
				serviceManager: "",
			},
			{
				id: "7",
				name: "Janet Samuel",
				email: "<EMAIL>",
				phone: "****** 94999",
				role: "Location Manager",
				status: "Active",
				dateOnboarded: "22 Oct 2020, 11:40 PM",
				avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b5c4?w=40&h=40&fit=crop&crop=face",
				socialRoles: ["Location Manager"],
				roleDetails: {
					"Location Manager": [
						"University of Toronto, Ontario Research Centre",
					],
				},
				locationAccess: [
					"University of Toronto, Ontario Research Centre",
				],
				timeZone: "EST (UTC-5)",
				autoMessage: "Happy to assist with your location needs!",
				serviceManager: "",
			},
		],
		pagination: {
			page: 1,
			limit: 12,
			total: 7,
			totalPages: 1,
		},
	};

	// Debounced search filter
	useEffect(() => {
		const timer = setTimeout(() => {
			setFilters((prev) => ({
				...prev,
				search: searchTerm || undefined,
				page: 1,
			}));
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	const handleFilterChange = (newFilters: Partial<TeamMembersFilters>) => {
		setFilters((prev) => ({ ...prev, ...newFilters, page: 1 }));
	};

	const handleSelectAll = (checked: boolean) => {
		if (checked && teamMembersData?.data) {
			setSelectedMembers(teamMembersData.data.map((member) => member.id));
		} else {
			setSelectedMembers([]);
		}
	};

	const handleMemberSelection = (memberId: string, selected: boolean) => {
		if (selected) {
			setSelectedMembers((prev) => [...prev, memberId]);
		} else {
			setSelectedMembers((prev) => prev.filter((id) => id !== memberId));
		}
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleAddMember = async (data: any) => {
		console.log("Adding new member:", data);
		setShowAddMemberForm(false);
	};

	const handleViewMember = (member: TeamMember) => {
		setSelectedMember(member);
		setShowMemberDetails(true);
	};

	const handleEditMember = (member: TeamMember) => {
		setSelectedMember(member);
		setShowEditMember(true);
	};

	const handleEditFromView = (member: TeamMember) => {
		setShowMemberDetails(false);
		setSelectedMember(member);
		setShowEditMember(true);
	};

	const handleUpdateMember = async (data: any) => {
		console.log("Updating member:", data);
		setShowEditMember(false);
		// You would typically make an API call here to update the member
	};

	const handleApplyFilters = (filterData: any) => {
		console.log("Applying filters:", filterData);
	};

	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case "Active":
				return "default";
			case "Unverified":
				return "secondary";
			case "Pending":
				return "outline";
			default:
				return "default";
		}
	};

	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-between py-3 pl-4">
				<h1 className="text-2xl font-bold">Team Members</h1>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className="cursor-pointer"
						size="icon"
						onClick={() => setShowFilterSheet(true)}
					>
						<Funnel className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						className="hover:bg-primary/90 cursor-pointer text-black hover:text-white"
						onClick={() => setShowAddMemberForm(true)}
					>
						<Plus className="mr-2 h-4 w-4" />
						Add a Member
					</Button>
				</div>
			</div>

			{/* Table */}
			<div className="flex w-full flex-col overflow-hidden rounded-lg border border-zinc-200">
				<div className="text-muted flex h-12 items-center justify-between border-b bg-gray-50 py-1 pl-4">
					<div className="flex items-center pr-4">
						<Checkbox
							label=""
							checked={
								selectedMembers.length ===
									teamMembersData?.data?.length &&
								teamMembersData?.data?.length > 0
							}
							className="cursor-pointer"
							onCheckedChange={handleSelectAll}
						/>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Team Member
							</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Phone
							</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Role
							</p>
						</div>
					</div>
					<div className="flex-0.5 flex items-center px-3">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Status
							</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Date Onboarded
							</p>
						</div>
					</div>
					<div className="flex w-16 items-center px-3">
						<div className="flex items-center gap-3">
							<p></p>
						</div>
					</div>
				</div>

				{/* Team Members List */}
				{teamMembersData && (
					<>
						{teamMembersData?.data?.length === 0 ? (
							<div className="py-12 text-center">
								<Users className="mx-auto h-12 w-12 text-gray-400" />
								<h3 className="mt-2 text-sm font-medium text-gray-900">
									No team members found
								</h3>
								<p className="mt-1 text-sm text-gray-500">
									Get started by adding your first team
									member.
								</p>
								<Button
									className="mt-4"
									onClick={() => setShowAddMemberForm(true)}
								>
									<Plus className="mr-2 h-4 w-4" />
									Add Member
								</Button>
							</div>
						) : (
							<div className="flex flex-col">
								{teamMembersData?.data?.map(
									(member: TeamMember) => (
										<div
											onClick={() =>
												handleViewMember(member)
											}
											key={member.id}
											className="flex h-16 cursor-pointer items-center justify-between border-b border-gray-100 px-4 hover:bg-gray-50"
										>
											<div className="flex items-center pr-4">
												<Checkbox
													label=""
													checked={selectedMembers.includes(
														member.id
													)}
													className="cursor-pointer"
													onCheckedChange={(
														selected
													) =>
														handleMemberSelection(
															member.id,
															selected
														)
													}
												/>
											</div>
											<div className="flex flex-1 items-center px-3">
												<div className="flex items-center gap-3">
													<Avatar className="h-10 w-10">
														<AvatarImage
															src={
																member.avatar ||
																undefined
															}
															alt={member.name}
														/>
														<AvatarFallback className="bg-gray-200 text-sm text-gray-600">
															{member.name
																.split(" ")
																.map(
																	(n) => n[0]
																)
																.join("")}
														</AvatarFallback>
													</Avatar>
													<div>
														<p className="text-sm font-medium text-gray-900">
															{member.name}
														</p>
														<p className="text-xs text-gray-500">
															{member.email}
														</p>
													</div>
												</div>
											</div>
											<div className="flex flex-1 items-center px-3">
												<p className="text-sm text-gray-600">
													{member.phone}
												</p>
											</div>
											<div className="flex flex-1 items-center px-3">
												<div className="flex flex-col gap-1">
													<p className="text-sm text-gray-900">
														{member.role}
													</p>
													{member.id === "4" && (
														<p className="text-xs text-gray-500">
															Team Member
														</p>
													)}
												</div>
											</div>
											<div className="flex-0.5 flex items-center px-3">
												<Badge
													variant={getStatusBadgeVariant(
														member.status
													)}
													className={
														(member.status ===
														"Active"
															? "border-green-200 bg-green-100 text-green-800 hover:bg-green-100"
															: member.status ===
																  "Unverified"
																? "border-yellow-200 bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
																: "bg-gray-100 text-gray-800 hover:bg-gray-100") +
														" h-6 min-w-[48px] px-2 py-0.5 text-xs"
													}
												>
													{member.status}
												</Badge>
											</div>
											<div className="flex flex-1 items-center px-3">
												<p className="text-sm text-gray-600">
													{member.dateOnboarded}
												</p>
											</div>
											<div className="flex w-16 items-center justify-end px-3">
												<div className="flex items-center gap-1">
													<Button
														variant="ghost"
														size="sm"
														className="h-8 w-8 border p-0"
														onClick={(e) => {
															e.stopPropagation();
															handleViewMember(
																member
															);
														}}
													>
														<Trash2 className="h-4 w-4 text-gray-400" />
													</Button>
													<Button
														variant="ghost"
														size="sm"
														className="h-8 w-8 border p-0"
														onClick={(e) => {
															e.stopPropagation();
															handleEditMember(
																member
															);
														}}
													>
														<Pen className="h-4 w-4 text-gray-400" />
													</Button>
													<Button
														variant="ghost"
														size="sm"
														className="h-8 w-8 border p-0"
													>
														<Info className="h-4 w-4 text-gray-400" />
													</Button>
												</div>
											</div>
										</div>
									)
								)}
							</div>
						)}

						{/* Pagination */}
						{teamMembersData?.pagination?.totalPages > 1 && (
							<div className="mt-8 flex items-center justify-center gap-2">
								<Button
									variant="outline"
									disabled={
										teamMembersData?.pagination?.page === 1
									}
									onClick={() =>
										handlePageChange(
											teamMembersData?.pagination?.page -
												1
										)
									}
								>
									Previous
								</Button>

								<span className="text-sm text-gray-600">
									Page {teamMembersData.pagination.page} of{" "}
									{teamMembersData?.pagination?.totalPages}
								</span>

								<Button
									variant="outline"
									disabled={
										teamMembersData?.pagination?.page ===
										teamMembersData?.pagination?.totalPages
									}
									onClick={() =>
										handlePageChange(
											teamMembersData?.pagination?.page +
												1
										)
									}
								>
									Next
								</Button>
							</div>
						)}
					</>
				)}
			</div>

			{/* Add Member Sheet */}
			<AddMemberSheet
				open={showAddMemberForm}
				onOpenChange={setShowAddMemberForm}
				onSubmit={handleAddMember}
			/>

			{/* View Member Sheet */}
			<ViewMemberSheet
				open={showMemberDetails}
				onOpenChange={setShowMemberDetails}
				member={selectedMember}
				onEdit={handleEditFromView}
			/>

			{/* Edit Member Sheet */}
			<EditMemberSheet
				open={showEditMember}
				onOpenChange={setShowEditMember}
				member={selectedMember}
				onSubmit={handleUpdateMember}
			/>
		</div>
	);
}
