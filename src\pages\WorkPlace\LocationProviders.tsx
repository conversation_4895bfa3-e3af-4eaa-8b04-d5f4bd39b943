import { useEffect, useState, type FC } from "react";
import { LocationDetailsCard } from "@/features/locations";
import { LocationProvidersList } from "@/features/providers";
import { useUIStore } from "@/stores/uiStore";
import type { LocationDetails } from "@/features/locations/components/LocationDetailsCard";
import LocationDetailsSheet from "@/features/locations/components/sheets/location-details/LocationDetailsSheet";

const LocationProviders: FC = () => {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setCurrentPageTitle = useUIStore(
		(state) => state.setCurrentPageTitle
	);
	const [openOrganizationDetailsSheet, setOpenOrganizationDetailsSheet] =
		useState(false);

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Workplace",
				href: "/dashboard/workplace",
			},
			{
				label: "Organizations",
				href: "/dashboard/workplace/locations",
			},
			{
				label: "Providers",
				isCurrentPage: true,
			},
		]);
		setCurrentPageTitle("Location & Providers");

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs, setCurrentPageTitle]);

	const handleView = (location: LocationDetails) => {
		console.log("View:", location.name);
		setOpenOrganizationDetailsSheet(true);
	};

	return (
		<div className="flex flex-col gap-4 py-6">
			<LocationDetailsCard
				location={{
					id: "org-1",
					name: "HealthCare Plus Medical Center",
					address:
						"123 Main Street, Downtown Medical District, NY 10001",
					rating: 4,
					locationsCount: 4,
					providersCount: 20,
					averageWaitTime: "2 hr 30 mins",
				}}
				onView={handleView}
				onEdit={(location) => console.log("Edit:", location.name)}
				onDelete={(location) => console.log("Delete:", location.name)}
			/>
			<LocationProvidersList />
			<LocationDetailsSheet
				open={openOrganizationDetailsSheet}
				onClose={() => setOpenOrganizationDetailsSheet(false)}
			/>
		</div>
	);
};

export default LocationProviders;
