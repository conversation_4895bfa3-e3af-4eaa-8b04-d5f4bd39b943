import moment from "moment";
import { Views } from "react-big-calendar";

export const VIEW_OPTIONS = [
    { id: Views.DAY, label: "Day" },
    { id: Views.WEEK, label: "Week" },
    { id: Views.MONTH, label: "Month" },
];

const APPOINTMENT_COLORS = {
    green: {
        color: "#F2FBF4",
        border: "#C3EFCE"
    },
    yellow: {
        color: "#FFFBEB",
        border: "#FDE68A",
    },
    pink: {
        color: "#FEF2F2",
        border: "#FECACA",
    }
}

// Function to get colors for appointments in a time slot
export const getAppointmentColors = (appointmentCount: number) => {
    const colors = Object.values(APPOINTMENT_COLORS);

    if (appointmentCount === 1) {
        // Single appointment: randomly pick one color
        return [colors[Math.floor(Math.random() * colors.length)]];
    }
    else if (appointmentCount === 2) {
        // Two appointments: pick 2 different colors randomly
        const shuffled = [...colors].sort(() => Math.random() - 0.5);
        return [shuffled[0], shuffled[1]];
    }
    else if (appointmentCount === 3) {
        // Three appointments: first is yellow, other two are pink and green
        const otherColors = [APPOINTMENT_COLORS.pink, APPOINTMENT_COLORS.green];
        const shuffled = otherColors.sort(() => Math.random() - 0.5);
        return [APPOINTMENT_COLORS.yellow, shuffled[0], shuffled[1]];
    }
    else {
        // More than 3: cycle through all colors
        const result = [];
        for (let i = 0; i < appointmentCount; i++) {
            result.push(colors[i % colors.length]);
        }
        return result;
    }
};

export function getFormattedHeader(date: Date, view: string) {
    if (view === 'day') {
        return moment(date).format('ddd DD MMMM YYYY');
    } else if (view === 'week') {
        return `Week of ${moment(date).format('ddd DD MMMM YYYY')}`;
    } else if (view === 'month') {
        return moment(date).format('MMMM YYYY');
    }
    return '';
}

// Utility: Get all days (Mon-Sun) for the week containing the given date
export function getWeekDays(date: Date): Date[] {
    const curr = new Date(date);
    const day = curr.getDay();
    const diff = (day === 0 ? -6 : 1) - day; // if Sunday, go back 6 days
    const monday = new Date(curr);
    monday.setDate(curr.getDate() + diff);
    monday.setHours(0, 0, 0, 0);
    return Array.from({ length: 7 }).map((_, i) => {
        const d = new Date(monday);
        d.setDate(monday.getDate() + i);
        return d;
    });
}